<?php
/**
 * South Safari Partnership Platform - Security Functions
 * Version 2.0 - Partnership-Centric & Feature-Focused
 * Created: 2025-01-18
 */

// Prevent direct access
if (!defined('SS_INIT')) {
    die('Direct access not permitted');
}

class Security {
    private static $instance = null;
    private $csrfTokens = [];

    private function __construct() {
        $this->initCSRF();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize CSRF protection
     */
    private function initCSRF() {
        if (!isset($_SESSION['csrf_tokens'])) {
            $_SESSION['csrf_tokens'] = [];
        }
        $this->csrfTokens = &$_SESSION['csrf_tokens'];
    }

    /**
     * Generate CSRF token
     */
    public function generateCSRFToken($action = 'default') {
        $token = bin2hex(random_bytes(32));
        $this->csrfTokens[$action] = [
            'token' => $token,
            'expires' => time() + 3600 // 1 hour
        ];
        
        // Clean up expired tokens
        $this->cleanupExpiredTokens();
        
        return $token;
    }

    /**
     * Validate CSRF token
     */
    public function validateCSRFToken($token, $action = 'default') {
        if (!isset($this->csrfTokens[$action])) {
            return false;
        }

        $storedToken = $this->csrfTokens[$action];
        
        // Check if token has expired
        if (time() > $storedToken['expires']) {
            unset($this->csrfTokens[$action]);
            return false;
        }

        // Validate token
        $isValid = hash_equals($storedToken['token'], $token);
        
        // Remove token after use (one-time use)
        if ($isValid) {
            unset($this->csrfTokens[$action]);
        }
        
        return $isValid;
    }

    /**
     * Clean up expired CSRF tokens
     */
    private function cleanupExpiredTokens() {
        $currentTime = time();
        foreach ($this->csrfTokens as $action => $tokenData) {
            if ($currentTime > $tokenData['expires']) {
                unset($this->csrfTokens[$action]);
            }
        }
    }

    /**
     * Generate CSRF token input field
     */
    public function getCSRFInput($action = 'default') {
        $token = $this->generateCSRFToken($action);
        return "<input type='hidden' name='csrf_token' value='{$token}'>";
    }

    /**
     * Validate request method
     */
    public function validateRequestMethod($allowedMethods) {
        $currentMethod = $_SERVER['REQUEST_METHOD'];
        $allowedMethods = is_array($allowedMethods) ? $allowedMethods : [$allowedMethods];
        
        if (!in_array($currentMethod, $allowedMethods)) {
            http_response_code(405);
            die('Method not allowed');
        }
        
        return true;
    }

    /**
     * Sanitize input to prevent XSS
     */
    public function sanitizeInput($input, $allowHTML = false) {
        if (is_array($input)) {
            return array_map(function($item) use ($allowHTML) {
                return $this->sanitizeInput($item, $allowHTML);
            }, $input);
        }

        if (!$allowHTML) {
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        }

        // If HTML is allowed, use a whitelist approach
        $allowedTags = '<p><br><strong><em><u><a><ul><ol><li><h1><h2><h3><h4><h5><h6>';
        return strip_tags(trim($input), $allowedTags);
    }

    /**
     * Validate file upload
     */
    public function validateFileUpload($file) {
        $errors = [];

        // Check if file was uploaded
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            $errors[] = 'No file was uploaded or upload failed.';
            return $errors;
        }

        // Check file size
        if ($file['size'] > MAX_FILE_SIZE) {
            $errors[] = 'File size exceeds maximum allowed size (' . formatFileSize(MAX_FILE_SIZE) . ').';
        }

        // Check file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, ALLOWED_FILE_TYPES)) {
            $errors[] = 'File type not allowed. Allowed types: ' . implode(', ', ALLOWED_FILE_TYPES);
        }

        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        $allowedMimeTypes = [
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'zip' => 'application/zip',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png'
        ];

        if (!isset($allowedMimeTypes[$extension]) || $mimeType !== $allowedMimeTypes[$extension]) {
            $errors[] = 'Invalid file type detected.';
        }

        // Check for malicious content (basic check)
        $fileContent = file_get_contents($file['tmp_name'], false, null, 0, 1024);
        if (strpos($fileContent, '<?php') !== false || strpos($fileContent, '<script') !== false) {
            $errors[] = 'File contains potentially malicious content.';
        }

        return $errors;
    }

    /**
     * Rate limiting
     */
    public function checkRateLimit($action, $identifier, $maxAttempts = 5, $timeWindow = 300) {
        $key = "rate_limit_{$action}_{$identifier}";
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = [
                'attempts' => 0,
                'first_attempt' => time()
            ];
        }

        $data = $_SESSION[$key];
        
        // Reset if time window has passed
        if (time() - $data['first_attempt'] > $timeWindow) {
            $_SESSION[$key] = [
                'attempts' => 1,
                'first_attempt' => time()
            ];
            return true;
        }

        // Check if limit exceeded
        if ($data['attempts'] >= $maxAttempts) {
            return false;
        }

        // Increment attempts
        $_SESSION[$key]['attempts']++;
        return true;
    }

    /**
     * Generate secure password
     */
    public function generateSecurePassword($length = 12) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[random_int(0, strlen($chars) - 1)];
        }
        
        return $password;
    }

    /**
     * Validate password strength
     */
    public function validatePasswordStrength($password) {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long.';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter.';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter.';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number.';
        }
        
        if (!preg_match('/[^a-zA-Z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character.';
        }
        
        return $errors;
    }

    /**
     * Log security event
     */
    public function logSecurityEvent($event, $details = []) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip_address' => getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'user_id' => getCurrentUser()['id'] ?? null,
            'details' => $details
        ];
        
        error_log('SECURITY EVENT: ' . json_encode($logData));
        
        // You could also store this in a dedicated security_log table
        if (function_exists('db')) {
            try {
                db()->logActivity(
                    $logData['user_id'],
                    'security_event',
                    'security',
                    null,
                    null,
                    $logData
                );
            } catch (Exception $e) {
                error_log('Failed to log security event to database: ' . $e->getMessage());
            }
        }
    }

    /**
     * Check for suspicious activity
     */
    public function checkSuspiciousActivity() {
        $suspiciousPatterns = [
            'sql_injection' => [
                '/union\s+select/i',
                '/drop\s+table/i',
                '/insert\s+into/i',
                '/delete\s+from/i'
            ],
            'xss' => [
                '/<script/i',
                '/javascript:/i',
                '/on\w+\s*=/i'
            ],
            'path_traversal' => [
                '/\.\.\//i',
                '/\.\.\\\\/i'
            ]
        ];

        $input = array_merge($_GET, $_POST, $_COOKIE);
        
        foreach ($input as $key => $value) {
            if (is_string($value)) {
                foreach ($suspiciousPatterns as $type => $patterns) {
                    foreach ($patterns as $pattern) {
                        if (preg_match($pattern, $value)) {
                            $this->logSecurityEvent('suspicious_activity', [
                                'type' => $type,
                                'pattern' => $pattern,
                                'input_key' => $key,
                                'input_value' => substr($value, 0, 100)
                            ]);
                            
                            // You might want to block the request here
                            // http_response_code(403);
                            // die('Suspicious activity detected');
                        }
                    }
                }
            }
        }
    }

    /**
     * Validate referrer
     */
    public function validateReferrer($allowedDomains = []) {
        if (empty($allowedDomains)) {
            $allowedDomains = [parse_url(BASE_URL, PHP_URL_HOST)];
        }

        $referrer = $_SERVER['HTTP_REFERER'] ?? '';
        
        if (empty($referrer)) {
            return false; // No referrer
        }

        $referrerHost = parse_url($referrer, PHP_URL_HOST);
        
        return in_array($referrerHost, $allowedDomains);
    }

    /**
     * Secure headers
     */
    public function setSecurityHeaders() {
        // Prevent clickjacking
        header('X-Frame-Options: DENY');
        
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // XSS protection
        header('X-XSS-Protection: 1; mode=block');
        
        // Referrer policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Content Security Policy (basic)
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; img-src 'self' data: ui-avatars.com; font-src 'self' cdnjs.cloudflare.com;");
        
        // HTTPS enforcement (if using HTTPS)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
    }
}

// Global security helper functions
function security() {
    return Security::getInstance();
}

function generateCSRFToken($action = 'default') {
    return security()->generateCSRFToken($action);
}

function validateCSRFToken($token, $action = 'default') {
    return security()->validateCSRFToken($token, $action);
}

function getCSRFInput($action = 'default') {
    return security()->getCSRFInput($action);
}

function sanitizeInput($input, $allowHTML = false) {
    return security()->sanitizeInput($input, $allowHTML);
}

function validateFileUpload($file) {
    return security()->validateFileUpload($file);
}

function checkRateLimit($action, $identifier, $maxAttempts = 5, $timeWindow = 300) {
    return security()->checkRateLimit($action, $identifier, $maxAttempts, $timeWindow);
}

function logSecurityEvent($event, $details = []) {
    return security()->logSecurityEvent($event, $details);
}

function setSecurityHeaders() {
    return security()->setSecurityHeaders();
}

// Automatically check for suspicious activity on every request
if (!defined('SKIP_SECURITY_CHECK')) {
    security()->checkSuspiciousActivity();
}
