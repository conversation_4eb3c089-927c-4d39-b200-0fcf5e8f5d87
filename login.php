<?php
/**
 * South Safari Partnership Platform - Partnership Collaborator Login
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once 'includes/init.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    redirect($user['role'] === 'admin' ? ADMIN_URL : COLLABORATOR_URL);
}

$errors = [];
$email = '';

if (($_SERVER['REQUEST_METHOD'] ?? '') === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        $email = sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $rememberMe = isset($_POST['remember_me']);

        // Basic validation
        if (empty($email)) {
            $errors[] = 'Email address is required.';
        }

        if (empty($password)) {
            $errors[] = 'Password is required.';
        }

        // Check rate limiting
        if (!checkRateLimit('login', getClientIP(), 5, 900)) { // 5 attempts per 15 minutes
            $errors[] = 'Too many login attempts. Please try again later.';
        }

        // If no errors, attempt login
        if (empty($errors)) {
            try {
                $result = auth()->login($email, $password, $rememberMe);
                
                if ($result['success']) {
                    // Check for redirect URL
                    $redirectUrl = $_SESSION['redirect_after_login'] ?? $result['redirect_url'];
                    unset($_SESSION['redirect_after_login']);
                    
                    redirect($redirectUrl, 'Welcome back to South Safari!', 'success');
                }
            } catch (Exception $e) {
                $errors[] = $e->getMessage();
                logSecurityEvent('login_failed', [
                    'email' => $email,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}

$pageTitle = 'Sign In to Your Partnership Account';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }
        
        .auth-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .auth-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-body {
            padding: 2rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 176, 116, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #008c5a;
            border-color: #008c5a;
        }
        
        .partnership-welcome {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 1rem;
        }
        
        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #dee2e6;
        }
        
        .divider span {
            background: white;
            padding: 0 1rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1 class="h3 mb-2">
                    <i class="fas fa-handshake me-2"></i>
                    Welcome Back
                </h1>
                <p class="mb-0">Sign in to your partnership account</p>
            </div>
            
            <div class="auth-body">
                <div class="partnership-welcome">
                    <i class="fas fa-users text-primary fs-2 mb-2"></i>
                    <p class="mb-0 small">Continue your partnership journey with South Safari</p>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo escape($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <?php echo getCSRFInput(); ?>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo escape($email); ?>" required autofocus>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                        <label class="form-check-label" for="remember_me">
                            Remember me for 7 days
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In to Partnership Portal
                    </button>
                </form>

                <div class="forgot-password">
                    <a href="forgot-password.php" class="text-muted">
                        <i class="fas fa-key me-1"></i>Forgot your password?
                    </a>
                </div>

                <div class="divider">
                    <span>New to South Safari?</span>
                </div>

                <div class="text-center">
                    <a href="register.php" class="btn btn-outline-primary w-100 mb-3">
                        <i class="fas fa-user-plus me-2"></i>Start Partnership Journey
                    </a>
                    <p class="mb-0">
                        <a href="index.php" class="text-muted">
                            <i class="fas fa-arrow-left me-1"></i>Back to Homepage
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-focus on password field if email is already filled
        document.addEventListener('DOMContentLoaded', function() {
            const emailField = document.getElementById('email');
            const passwordField = document.getElementById('password');
            
            if (emailField.value.trim() !== '') {
                passwordField.focus();
            }
        });
    </script>
</body>
</html>
