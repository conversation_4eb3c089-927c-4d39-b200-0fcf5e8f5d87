-- South Safari Partnership Platform Database Schema
-- Version 2.0 - Partnership-Centric & Feature-Focused
-- Created: 2025-01-18

-- Create database
CREATE DATABASE IF NOT EXISTS south_safari_partnerships CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE south_safari_partnerships;

-- Users table (partnership collaborators and admins)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    country VARCHAR(100),
    phone VARCHAR(50),
    company_name VARCHAR(255),
    portfolio_url VARCHAR(500),
    professional_background TEXT,
    role ENUM('admin', 'collaborator') DEFAULT 'collaborator',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- Partnership opportunities table (formerly projects)
CREATE TABLE partnership_opportunities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    category VARCHAR(100) NOT NULL,
    short_description TEXT NOT NULL,
    full_description LONGTEXT NOT NULL,
    product_features JSON, -- Store feature badges as JSON array
    partnership_model ENUM('revenue_share', 'equity_partnership', 'profit_sharing', 'hybrid_model', 'service_model') NOT NULL,
    revenue_split VARCHAR(50), -- e.g., "50/50", "60/40"
    market_opportunity TEXT,
    expected_timeline VARCHAR(100),
    partnership_terms TEXT,
    requirements TEXT,
    status ENUM('active', 'inactive', 'filled', 'paused') DEFAULT 'active',
    featured BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 0,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_featured (featured),
    INDEX idx_display_order (display_order),
    FULLTEXT idx_search (title, short_description, full_description)
);

-- Partnership connections table (formerly applications)
CREATE TABLE partnership_connections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    opportunity_id INT NOT NULL,
    collaborator_id INT NOT NULL,
    relevant_experience TEXT NOT NULL,
    portfolio_items TEXT,
    proposed_approach TEXT NOT NULL,
    availability_date DATE,
    questions_for_admin TEXT,
    status ENUM('draft', 'submitted', 'reviewing', 'approved', 'rejected', 'withdrawn') DEFAULT 'draft',
    admin_notes TEXT,
    reviewed_by INT NULL,
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (opportunity_id) REFERENCES partnership_opportunities(id) ON DELETE CASCADE,
    FOREIGN KEY (collaborator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_collaborator (collaborator_id),
    INDEX idx_opportunity (opportunity_id),
    UNIQUE KEY unique_connection (opportunity_id, collaborator_id)
);

-- Active partnerships table
CREATE TABLE active_partnerships (
    id INT PRIMARY KEY AUTO_INCREMENT,
    opportunity_id INT NOT NULL,
    collaborator_id INT NOT NULL,
    connection_id INT NOT NULL,
    partnership_agreement TEXT,
    start_date DATE NOT NULL,
    end_date DATE NULL,
    status ENUM('active', 'completed', 'terminated', 'on_hold') DEFAULT 'active',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    revenue_generated DECIMAL(15,2) DEFAULT 0.00,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (opportunity_id) REFERENCES partnership_opportunities(id) ON DELETE CASCADE,
    FOREIGN KEY (collaborator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (connection_id) REFERENCES partnership_connections(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_collaborator (collaborator_id),
    INDEX idx_opportunity (opportunity_id)
);

-- Messages table for partnership communication
CREATE TABLE partnership_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    partnership_id INT NULL,
    connection_id INT NULL,
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    message_type ENUM('general', 'partnership_update', 'connection_status', 'system_notification') DEFAULT 'general',
    is_read BOOLEAN DEFAULT FALSE,
    parent_message_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (partnership_id) REFERENCES active_partnerships(id) ON DELETE CASCADE,
    FOREIGN KEY (connection_id) REFERENCES partnership_connections(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_message_id) REFERENCES partnership_messages(id) ON DELETE SET NULL,
    INDEX idx_recipient (recipient_id),
    INDEX idx_sender (sender_id),
    INDEX idx_read_status (is_read),
    INDEX idx_created (created_at)
);

-- File uploads table
CREATE TABLE partnership_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    partnership_id INT NULL,
    connection_id INT NULL,
    uploaded_by INT NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (partnership_id) REFERENCES active_partnerships(id) ON DELETE CASCADE,
    FOREIGN KEY (connection_id) REFERENCES partnership_connections(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_partnership (partnership_id),
    INDEX idx_connection (connection_id),
    INDEX idx_uploader (uploaded_by)
);

-- System settings table
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
);

-- Email templates table
CREATE TABLE email_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    template_key VARCHAR(100) UNIQUE NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body_html TEXT NOT NULL,
    body_text TEXT,
    variables JSON, -- Available template variables
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (template_key),
    INDEX idx_active (is_active)
);

-- Activity log table for audit trail
CREATE TABLE activity_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_created (created_at)
);

-- =====================================================
-- INITIAL DATA INSERTION
-- =====================================================

-- Insert initial admin user (password: admin123)
INSERT INTO users (email, password_hash, full_name, role, status, email_verified) VALUES
('<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/lewdBdXzpVMvyUBvq', 'South Safari Admin', 'admin', 'active', TRUE);

-- Insert system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'South Safari Partnership Platform', 'string', 'Website name', TRUE),
('site_tagline', 'Partner with SS and Stay Relevant', 'string', 'Website tagline', TRUE),
('admin_email', '<EMAIL>', 'string', 'Administrator email address', FALSE),
('smtp_host', '', 'string', 'SMTP server host', FALSE),
('smtp_port', '587', 'integer', 'SMTP server port', FALSE),
('smtp_username', '', 'string', 'SMTP username', FALSE),
('smtp_password', '', 'string', 'SMTP password', FALSE),
('max_file_size', '********', 'integer', 'Maximum file upload size in bytes (10MB)', FALSE),
('allowed_file_types', '["pdf","doc","docx","zip","jpg","jpeg","png"]', 'json', 'Allowed file upload types', FALSE),
('partnerships_per_page', '12', 'integer', 'Number of partnerships to display per page', TRUE),
('enable_email_verification', '1', 'boolean', 'Require email verification for new accounts', FALSE),
('enable_partnership_auto_approval', '0', 'boolean', 'Auto-approve partnership connections', FALSE);
