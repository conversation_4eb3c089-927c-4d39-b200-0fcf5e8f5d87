<?php
/**
 * South Safari Partnership Platform - Partnership Opportunity Details
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once 'includes/init.php';

// Get partnership opportunity by slug
$slug = $_GET['slug'] ?? '';
if (empty($slug)) {
    redirect('partnerships.php', 'Partnership opportunity not found.', 'error');
}

$opportunity = getPartnershipBySlug($slug);
if (!$opportunity) {
    redirect('partnerships.php', 'Partnership opportunity not found.', 'error');
}

// Check if user is logged in and has already connected
$hasConnection = false;
$existingConnection = null;
if (isLoggedIn()) {
    $user = getCurrentUser();
    $hasConnection = hasConnectionToOpportunity($opportunity['id'], $user['id']);
    if ($hasConnection) {
        $existingConnection = db()->fetchRow(
            "SELECT * FROM partnership_connections WHERE opportunity_id = ? AND collaborator_id = ?",
            [$opportunity['id'], $user['id']]
        );
    }
}

// Parse product features
$features = json_decode($opportunity['product_features'], true) ?: [];

$pageTitle = $opportunity['title'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <meta name="description" content="<?php echo escape($opportunity['short_description']); ?>">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--secondary-color);
            background-color: #f8f9fa;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .partnership-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            color: white;
            padding: 3rem 0;
        }
        
        .partnership-category {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .partnership-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            line-height: 1.2;
        }
        
        .partnership-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .partnership-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            margin-top: 1.5rem;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }
        
        .feature-badge {
            background: #E8F5F0;
            color: var(--primary-color);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-right: 0.75rem;
            margin-bottom: 0.75rem;
            display: inline-block;
            border: 1px solid rgba(0, 176, 116, 0.2);
        }
        
        .partnership-model-badge {
            background: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 30px;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .connect-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            position: sticky;
            top: 2rem;
        }
        
        .connect-button {
            background: white;
            color: var(--primary-color);
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s;
            width: 100%;
        }
        
        .connect-button:hover {
            background: #f8f9fa;
            color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .connect-button:disabled {
            background: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .status-submitted { background: #cce5ff; color: #0066cc; }
        .status-reviewing { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--secondary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }
        
        .requirement-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .requirement-icon {
            color: var(--primary-color);
            margin-right: 0.75rem;
            margin-top: 0.25rem;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        
        .timeline-icon {
            color: var(--primary-color);
            margin-right: 0.75rem;
        }
        
        @media (max-width: 768px) {
            .partnership-title {
                font-size: 2rem;
            }
            
            .partnership-meta {
                flex-direction: column;
                gap: 1rem;
            }
            
            .connect-card {
                position: static;
                margin-top: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-handshake me-2"></i>South Safari
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="partnerships.php">Partnerships</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#how-it-works">How It Works</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <?php $currentUser = getCurrentUser(); ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i><?php echo escape($currentUser['full_name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="<?php echo $currentUser['role'] === 'admin' ? 'admin/' : 'collaborator/'; ?>">
                                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">Sign In</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">Join Partnership</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Partnership Header -->
    <section class="partnership-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <span class="partnership-category"><?php echo escape($opportunity['category']); ?></span>
                    <h1 class="partnership-title"><?php echo escape($opportunity['title']); ?></h1>
                    <p class="partnership-subtitle"><?php echo escape($opportunity['short_description']); ?></p>
                    
                    <div class="partnership-meta">
                        <div class="meta-item">
                            <i class="fas fa-handshake"></i>
                            <span class="partnership-model-badge">
                                <?php echo escape($opportunity['revenue_split'] ?: PARTNERSHIP_MODELS[$opportunity['partnership_model']]); ?>
                            </span>
                        </div>
                        <?php if ($opportunity['expected_timeline']): ?>
                            <div class="meta-item">
                                <i class="fas fa-clock"></i>
                                <span><?php echo escape($opportunity['expected_timeline']); ?></span>
                            </div>
                        <?php endif; ?>
                        <div class="meta-item">
                            <i class="fas fa-calendar"></i>
                            <span>Posted <?php echo formatDate($opportunity['created_at']); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container my-5">
        <?php displayFlashMessage(); ?>
        
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Product Features -->
                <div class="content-card">
                    <h2 class="section-title">Product Features & Capabilities</h2>
                    <p class="text-muted mb-4">This partnership will deliver the following key features and capabilities:</p>
                    <div class="mb-4">
                        <?php foreach ($features as $feature): ?>
                            <span class="feature-badge"><?php echo escape($feature); ?></span>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Partnership Description -->
                <div class="content-card">
                    <h2 class="section-title">Partnership Overview</h2>
                    <div class="partnership-description">
                        <?php echo nl2br(escape($opportunity['full_description'])); ?>
                    </div>
                </div>

                <!-- Market Opportunity -->
                <?php if ($opportunity['market_opportunity']): ?>
                    <div class="content-card">
                        <h2 class="section-title">Market Opportunity</h2>
                        <div class="market-opportunity">
                            <?php echo nl2br(escape($opportunity['market_opportunity'])); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Partnership Terms -->
                <?php if ($opportunity['partnership_terms']): ?>
                    <div class="content-card">
                        <h2 class="section-title">Partnership Terms</h2>
                        <div class="partnership-terms">
                            <?php echo nl2br(escape($opportunity['partnership_terms'])); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Requirements -->
                <?php if ($opportunity['requirements']): ?>
                    <div class="content-card">
                        <h2 class="section-title">Partnership Requirements</h2>
                        <div class="requirements">
                            <?php 
                            $requirements = explode("\n", $opportunity['requirements']);
                            foreach ($requirements as $requirement) {
                                $requirement = trim($requirement);
                                if (!empty($requirement)) {
                                    echo '<div class="requirement-item">';
                                    echo '<i class="fas fa-check-circle requirement-icon"></i>';
                                    echo '<span>' . escape($requirement) . '</span>';
                                    echo '</div>';
                                }
                            }
                            ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Timeline -->
                <?php if ($opportunity['expected_timeline']): ?>
                    <div class="content-card">
                        <h2 class="section-title">Expected Timeline</h2>
                        <div class="timeline-item">
                            <i class="fas fa-clock timeline-icon"></i>
                            <span><?php echo escape($opportunity['expected_timeline']); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="connect-card">
                    <?php if (!isLoggedIn()): ?>
                        <h4 class="mb-3">Ready to Connect?</h4>
                        <p class="mb-4">Join our partnership community to connect with this opportunity.</p>
                        <a href="register.php" class="connect-button btn">
                            <i class="fas fa-user-plus me-2"></i>Join Partnership Community
                        </a>
                        <div class="mt-3">
                            <small>Already have an account? <a href="login.php" class="text-white"><u>Sign In</u></a></small>
                        </div>
                    <?php elseif ($hasConnection): ?>
                        <h4 class="mb-3">Connection Status</h4>
                        <div class="mb-3">
                            <span class="status-badge status-<?php echo $existingConnection['status']; ?>">
                                <?php echo CONNECTION_STATUSES[$existingConnection['status']] ?? ucfirst($existingConnection['status']); ?>
                            </span>
                        </div>
                        <p class="mb-4">
                            <?php if ($existingConnection['status'] === 'submitted'): ?>
                                Your connection request has been submitted and is awaiting review.
                            <?php elseif ($existingConnection['status'] === 'reviewing'): ?>
                                Your connection request is currently under review.
                            <?php elseif ($existingConnection['status'] === 'approved'): ?>
                                Congratulations! Your connection has been approved. We'll be in touch soon.
                            <?php elseif ($existingConnection['status'] === 'rejected'): ?>
                                Your connection request was not approved at this time. Feel free to explore other opportunities.
                            <?php endif; ?>
                        </p>
                        <a href="collaborator/connection-details.php?id=<?php echo $existingConnection['id']; ?>" class="connect-button btn">
                            <i class="fas fa-eye me-2"></i>View Connection Details
                        </a>
                    <?php else: ?>
                        <h4 class="mb-3">Connect with This Partnership</h4>
                        <p class="mb-4">Ready to start a meaningful partnership? Submit your connection request to begin the collaboration process.</p>
                        <a href="connect.php?opportunity=<?php echo $opportunity['id']; ?>" class="connect-button btn">
                            <i class="fas fa-handshake me-2"></i>Connect Now
                        </a>
                    <?php endif; ?>
                    
                    <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">
                    
                    <div class="text-start">
                        <h6 class="mb-3">Partnership Highlights</h6>
                        <div class="mb-2">
                            <i class="fas fa-chart-line me-2"></i>
                            <small><?php echo escape(PARTNERSHIP_MODELS[$opportunity['partnership_model']]); ?></small>
                        </div>
                        <?php if ($opportunity['revenue_split']): ?>
                            <div class="mb-2">
                                <i class="fas fa-percentage me-2"></i>
                                <small><?php echo escape($opportunity['revenue_split']); ?> Revenue Split</small>
                            </div>
                        <?php endif; ?>
                        <div class="mb-2">
                            <i class="fas fa-users me-2"></i>
                            <small>Collaborative Partnership</small>
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-globe-africa me-2"></i>
                            <small>Southern African Market</small>
                        </div>
                    </div>
                </div>

                <!-- Related Opportunities -->
                <?php 
                $relatedOpportunities = db()->fetchAll(
                    "SELECT * FROM partnership_opportunities 
                     WHERE category = ? AND id != ? AND status = 'active' 
                     ORDER BY featured DESC, created_at DESC 
                     LIMIT 3",
                    [$opportunity['category'], $opportunity['id']]
                );
                ?>
                
                <?php if (!empty($relatedOpportunities)): ?>
                    <div class="content-card mt-4">
                        <h5 class="mb-3">Related Partnerships</h5>
                        <?php foreach ($relatedOpportunities as $related): ?>
                            <div class="border-bottom pb-3 mb-3">
                                <h6 class="mb-1">
                                    <a href="partnership.php?slug=<?php echo $related['slug']; ?>" class="text-decoration-none">
                                        <?php echo escape($related['title']); ?>
                                    </a>
                                </h6>
                                <p class="text-muted small mb-2"><?php echo truncate($related['short_description'], 80); ?></p>
                                <small class="text-primary"><?php echo escape($related['revenue_split'] ?: PARTNERSHIP_MODELS[$related['partnership_model']]); ?></small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
