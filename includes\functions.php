<?php
/**
 * South Safari Partnership Platform - Utility Functions
 * Version 2.0 - Partnership-Centric & Feature-Focused
 * Created: 2025-01-18
 */

// Prevent direct access
if (!defined('SS_INIT')) {
    die('Direct access not permitted');
}

/**
 * Sanitize and escape output for HTML
 */
function escape($string) {
    return htmlspecialchars($string ?? '', ENT_QUOTES, 'UTF-8');
}

/**
 * Sanitize input data
 */
function sanitize($input) {
    if (is_array($input)) {
        return array_map('sanitize', $input);
    }
    return trim(strip_tags($input ?? ''));
}

/**
 * Generate a clean URL slug
 */
function generateSlug($string) {
    $slug = strtolower(trim($string));
    $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    return trim($slug, '-');
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'M j, Y') {
    if (!$date) return '';
    return date($format, strtotime($date));
}

/**
 * Format datetime for display
 */
function formatDateTime($datetime, $format = 'M j, Y g:i A') {
    if (!$datetime) return '';
    return date($format, strtotime($datetime));
}

/**
 * Time ago function
 */
function timeAgo($datetime) {
    if (!$datetime) return '';
    
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

/**
 * Truncate text with ellipsis
 */
function truncate($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . $suffix;
}

/**
 * Generate random string
 */
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

/**
 * Validate email address
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate URL
 */
function isValidUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Get client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * Redirect with message
 */
function redirect($url, $message = null, $type = 'info') {
    if ($message) {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }
    header("Location: $url");
    exit;
}

/**
 * Get and clear flash message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * Display flash message HTML
 */
function displayFlashMessage() {
    $flash = getFlashMessage();
    if ($flash) {
        $alertClass = [
            'success' => 'alert-success',
            'error' => 'alert-danger',
            'warning' => 'alert-warning',
            'info' => 'alert-info'
        ][$flash['type']] ?? 'alert-info';
        
        echo "<div class='alert {$alertClass} alert-dismissible fade show' role='alert'>";
        echo escape($flash['message']);
        echo "<button type='button' class='btn-close' data-bs-dismiss='alert'></button>";
        echo "</div>";
    }
}

/**
 * Generate pagination HTML
 */
function generatePagination($currentPage, $totalPages, $baseUrl, $params = []) {
    if ($totalPages <= 1) return '';
    
    $html = '<nav aria-label="Pagination"><ul class="pagination justify-content-center">';
    
    // Previous button
    if ($currentPage > 1) {
        $prevParams = array_merge($params, ['page' => $currentPage - 1]);
        $prevUrl = $baseUrl . '?' . http_build_query($prevParams);
        $html .= "<li class='page-item'><a class='page-link' href='{$prevUrl}'>Previous</a></li>";
    } else {
        $html .= "<li class='page-item disabled'><span class='page-link'>Previous</span></li>";
    }
    
    // Page numbers
    $start = max(1, $currentPage - 2);
    $end = min($totalPages, $currentPage + 2);
    
    if ($start > 1) {
        $firstParams = array_merge($params, ['page' => 1]);
        $firstUrl = $baseUrl . '?' . http_build_query($firstParams);
        $html .= "<li class='page-item'><a class='page-link' href='{$firstUrl}'>1</a></li>";
        if ($start > 2) {
            $html .= "<li class='page-item disabled'><span class='page-link'>...</span></li>";
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        if ($i == $currentPage) {
            $html .= "<li class='page-item active'><span class='page-link'>{$i}</span></li>";
        } else {
            $pageParams = array_merge($params, ['page' => $i]);
            $pageUrl = $baseUrl . '?' . http_build_query($pageParams);
            $html .= "<li class='page-item'><a class='page-link' href='{$pageUrl}'>{$i}</a></li>";
        }
    }
    
    if ($end < $totalPages) {
        if ($end < $totalPages - 1) {
            $html .= "<li class='page-item disabled'><span class='page-link'>...</span></li>";
        }
        $lastParams = array_merge($params, ['page' => $totalPages]);
        $lastUrl = $baseUrl . '?' . http_build_query($lastParams);
        $html .= "<li class='page-item'><a class='page-link' href='{$lastUrl}'>{$totalPages}</a></li>";
    }
    
    // Next button
    if ($currentPage < $totalPages) {
        $nextParams = array_merge($params, ['page' => $currentPage + 1]);
        $nextUrl = $baseUrl . '?' . http_build_query($nextParams);
        $html .= "<li class='page-item'><a class='page-link' href='{$nextUrl}'>Next</a></li>";
    } else {
        $html .= "<li class='page-item disabled'><span class='page-link'>Next</span></li>";
    }
    
    $html .= '</ul></nav>';
    return $html;
}

/**
 * Get partnership opportunity status badge
 */
function getStatusBadge($status) {
    $badges = [
        'active' => 'badge bg-success',
        'inactive' => 'badge bg-secondary',
        'filled' => 'badge bg-primary',
        'paused' => 'badge bg-warning'
    ];
    
    $class = $badges[$status] ?? 'badge bg-secondary';
    $label = OPPORTUNITY_STATUSES[$status] ?? ucfirst($status);
    
    return "<span class='{$class}'>{$label}</span>";
}

/**
 * Get connection status badge
 */
function getConnectionStatusBadge($status) {
    $badges = [
        'draft' => 'badge bg-secondary',
        'submitted' => 'badge bg-info',
        'reviewing' => 'badge bg-warning',
        'approved' => 'badge bg-success',
        'rejected' => 'badge bg-danger',
        'withdrawn' => 'badge bg-dark'
    ];
    
    $class = $badges[$status] ?? 'badge bg-secondary';
    $label = CONNECTION_STATUSES[$status] ?? ucfirst($status);
    
    return "<span class='{$class}'>{$label}</span>";
}

/**
 * Get partnership model badge
 */
function getPartnershipModelBadge($model) {
    $badges = [
        'revenue_share' => 'badge bg-primary',
        'equity_partnership' => 'badge bg-success',
        'profit_sharing' => 'badge bg-info',
        'hybrid_model' => 'badge bg-warning',
        'service_model' => 'badge bg-secondary'
    ];
    
    $class = $badges[$model] ?? 'badge bg-secondary';
    $label = PARTNERSHIP_MODELS[$model] ?? ucfirst(str_replace('_', ' ', $model));
    
    return "<span class='{$class}'>{$label}</span>";
}

/**
 * Format feature badges for display
 */
function formatFeatureBadges($features) {
    if (is_string($features)) {
        $features = json_decode($features, true);
    }
    
    if (!is_array($features)) {
        return '';
    }
    
    $html = '';
    foreach ($features as $feature) {
        $html .= "<span class='feature-badge'>" . escape($feature) . "</span> ";
    }
    
    return trim($html);
}

/**
 * Send JSON response
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Log error message
 */
function logError($message, $context = []) {
    $logMessage = date('Y-m-d H:i:s') . ' - ' . $message;
    if (!empty($context)) {
        $logMessage .= ' - Context: ' . json_encode($context);
    }
    error_log($logMessage);
}

/**
 * Check if request is AJAX
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Get current URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * Build URL with parameters
 */
function buildUrl($baseUrl, $params = []) {
    if (empty($params)) {
        return $baseUrl;
    }
    
    $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
    return $baseUrl . $separator . http_build_query($params);
}

/**
 * Check if file type is allowed
 */
function isAllowedFileType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, ALLOWED_FILE_TYPES);
}

/**
 * Generate secure filename
 */
function generateSecureFilename($originalFilename) {
    $extension = pathinfo($originalFilename, PATHINFO_EXTENSION);
    $basename = pathinfo($originalFilename, PATHINFO_FILENAME);
    $basename = preg_replace('/[^a-zA-Z0-9_-]/', '', $basename);
    $basename = substr($basename, 0, 50); // Limit length
    
    return $basename . '_' . time() . '_' . generateRandomString(8) . '.' . $extension;
}

/**
 * Create directory if it doesn't exist
 */
function ensureDirectoryExists($path) {
    if (!is_dir($path)) {
        mkdir($path, 0755, true);
    }
}

/**
 * Get user avatar URL (placeholder for now)
 */
function getUserAvatarUrl($user) {
    // For now, return a placeholder avatar
    $initial = strtoupper(substr($user['full_name'], 0, 1));
    return "https://ui-avatars.com/api/?name=" . urlencode($user['full_name']) . "&background=00B074&color=fff&size=40";
}

/**
 * Format currency (South African Rand)
 */
function formatCurrency($amount) {
    return 'R ' . number_format($amount, 2);
}

/**
 * Get partnership opportunity URL
 */
function getPartnershipUrl($opportunity) {
    return BASE_URL . 'partnership/' . $opportunity['slug'];
}

/**
 * Check if user can access partnership
 */
function canAccessPartnership($opportunityId, $userId = null) {
    if (!$userId) {
        $user = getCurrentUser();
        $userId = $user ? $user['id'] : null;
    }
    
    if (!$userId) return false;
    
    // Admin can access all
    if (isAdmin()) return true;
    
    // Check if user has connection to this opportunity
    return dbExists('partnership_connections', 
        'opportunity_id = ? AND collaborator_id = ?', 
        [$opportunityId, $userId]
    );
}
