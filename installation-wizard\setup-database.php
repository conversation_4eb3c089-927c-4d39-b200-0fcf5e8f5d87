<?php
/**
 * South Safari Partnership Platform - Quick Database Setup
 * This script creates the database and tables if they don't exist
 */

// Allow direct access for this setup script
define('SS_DIRECT_ACCESS', true);

// Database configuration
$dbHost = 'localhost';
$dbName = 'south_safari';
$dbUser = 'root';
$dbPass = '';

echo "<h2>South Safari Database Setup</h2>";

try {
    // Connect to MySQL server (without specifying database)
    $dsn = "mysql:host={$dbHost};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbUser, $dbPass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "<p>✅ Connected to MySQL server successfully</p>";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ Database '{$dbName}' created/verified</p>";
    
    // Use the database
    $pdo->exec("USE `{$dbName}`");
    echo "<p>✅ Using database '{$dbName}'</p>";
    
    // Check if schema file exists
    $schemaFile = __DIR__ . '/database/south_safari_schema.sql';
    if (!file_exists($schemaFile)) {
        echo "<p>❌ Schema file not found: {$schemaFile}</p>";
        exit;
    }
    
    // Read and execute schema
    $schema = file_get_contents($schemaFile);
    if (!$schema) {
        echo "<p>❌ Could not read schema file</p>";
        exit;
    }
    
    // Split statements and execute
    $statements = explode(';', $schema);
    $executedStatements = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                $executedStatements++;
            } catch (PDOException $e) {
                // Ignore "table already exists" errors
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "<p>⚠️ Warning executing statement: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    echo "<p>✅ Executed {$executedStatements} SQL statements</p>";
    
    // Check if sample data file exists and load it
    $sampleDataFile = __DIR__ . '/database/sample_data.sql';
    if (file_exists($sampleDataFile)) {
        $sampleData = file_get_contents($sampleDataFile);
        if ($sampleData) {
            $statements = explode(';', $sampleData);
            $sampleStatements = 0;
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    try {
                        $pdo->exec($statement);
                        $sampleStatements++;
                    } catch (PDOException $e) {
                        // Ignore duplicate entry errors
                        if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                            echo "<p>⚠️ Warning loading sample data: " . $e->getMessage() . "</p>";
                        }
                    }
                }
            }
            
            echo "<p>✅ Loaded sample data ({$sampleStatements} statements)</p>";
        }
    }
    
    // Verify tables exist
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>✅ Database contains " . count($tables) . " tables:</p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>{$table}</li>";
    }
    echo "</ul>";
    
    // Create admin user if it doesn't exist
    $adminExists = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'")->fetchColumn();
    
    if ($adminExists == 0) {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (email, password_hash, full_name, role, status, email_verified, created_at) 
            VALUES (?, ?, ?, 'admin', 'active', 1, NOW())
        ");
        $stmt->execute(['<EMAIL>', $adminPassword, 'Admin User']);
        echo "<p>✅ Created default admin user (<EMAIL> / admin123)</p>";
    } else {
        echo "<p>✅ Admin user already exists</p>";
    }
    
    echo "<h3>✅ Database Setup Complete!</h3>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php'>Visit Homepage</a></li>";
    echo "<li><a href='admin/'>Access Admin Panel</a> (<EMAIL> / admin123)</li>";
    echo "<li><a href='register.php'>Test Registration</a></li>";
    echo "<li><a href='partnerships.php'>Browse Partnerships</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p>❌ Database setup failed: " . $e->getMessage() . "</p>";
    echo "<p><strong>Common Solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Make sure XAMPP MySQL is running</li>";
    echo "<li>Check database credentials in includes/config.php</li>";
    echo "<li>Ensure MySQL port 3306 is available</li>";
    echo "</ul>";
}
?>
