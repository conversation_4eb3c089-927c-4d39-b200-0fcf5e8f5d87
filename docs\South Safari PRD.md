# SOUTH SAFARI PLATFORM
## Product Requirements Document (PRD) for AI Development

### Version 2.0 | 2025 - Partnership-Centric & Feature-Focused

---

## 1. PROJECT OVERVIEW

### 1.1 Product Name
South Safari Partnership Platform

### 1.2 Product Description
A partnership facilitation platform connecting skilled professionals with Southern African markets. The platform showcases partnership opportunities, manages partnership connections, and provides collaboration tools for active partnerships. Focus is on building meaningful partnerships through feature-rich product presentations rather than technical implementation details.

### 1.3 Platform Features
- **Partnership Discovery**: Advanced search and filtering for partnership opportunities
- **Feature-Rich Presentations**: Product capability showcases instead of technical specifications
- **Connection System**: "Connect" functionality replacing traditional application processes
- **Collaboration Tools**: Partnership management and communication features
- **Revenue Sharing Models**: Transparent partnership terms and profit-sharing structures
- **Partnership Analytics**: Performance tracking and partnership success metrics

### 1.4 Key Design Directive
**IMPORTANT**: The platform design emphasizes partnership collaboration over traditional job marketplace approaches. Features product capabilities and partnership opportunities with a focus on collaborative ventures. Uses green color scheme (#00B074) to represent growth and partnership success.

---

## 2. USER ROLES & PERMISSIONS

### 2.1 Admin (Super User)
- Full access to all platform features
- Manage projects, partnership collaborators, content
- View all communications and data
- Export capabilities

### 2.2 Partnership Collaborators
- Discover available partnership opportunities
- Connect with partnership opportunities
- Access collaborative project dashboards
- Communicate with partnership coordinators
- Share and access partnership documents

### 2.3 Visitors (Non-registered)
- Browse partnership opportunities
- View platform information
- Contact via public forms

---

## 3. CORE FEATURES - MVP

### 3.1 Public Website

#### 3.1.1 Homepage
**Layout**: Partnership-focused design with these sections:
- **Hero Section**:
  - Headline: "Build Meaningful Partnerships That Last"
  - Subheadline: "Partner with SS and Stay Relevant"
  - Search bar (searches partnership opportunities)
  - CTA buttons: "Explore Partnerships" and "Start Partnership"

- **Featured Projects Section**:
  - Display 8 partnership opportunity cards in grid (2x4)
  - Each card shows: Title, Brief Description, Product Feature badges, "Connect" button

- **Statistics Section**:
  - Active Partnerships
  - Partners Onboarded
  - Projects Launched
  - Revenue Generated

- **How It Works Section**:
  - 4 steps with icons
  - Step 1: Discover Partnerships
  - Step 2: Connect & Collaborate
  - Step 3: Partnership Agreement
  - Step 4: Launch & Prosper

- **Why Choose South Safari Section**: 
  - 6 benefit cards with icons
  - Benefits: Long-term Revenue, Local Support, Market Access, Fair Terms, Growth Potential, AI-Ready Future

- **Top Partners Section**:
  - Showcase successful partnerships
  - Partnership collaborator testimonials

- **Footer**: 
  - About, Contact, Projects, Terms, Privacy
  - Newsletter signup
  - Social media links

#### 3.1.2 Partnership Opportunities Listing Page
- Grid layout (3 columns)
- Filters: Category, Product Features, Partnership Type
- Each partnership opportunity card displays:
  - Partnership title
  - Short description (100 chars)
  - Product feature badges
  - Partnership model
  - "Connect" button

#### 3.1.3 Partnership Details Page
- Full partnership opportunity description
- Product feature specifications
- Partnership terms preview
- Expected timeline
- Market opportunity details
- Connection form (if not logged in, prompt to register)

#### 3.1.4 About Page
- Company story
- Mission/Vision
- Core values
- Team section (optional for MVP)

#### 3.1.5 Contact Page
- Contact form
- Email addresses
- WhatsApp number
- Physical address

### 3.2 Partnership Collaborator Portal

#### 3.2.1 Registration/Login
**Registration Fields**:
- Full Name*
- Email*
- Password*
- Country*
- Phone Number
- Company Name (optional)
- Portfolio URL
- Professional Background (optional)

**Login**: Email + Password

#### 3.2.2 Partnership Collaborator Dashboard
**Main sections**:
- Welcome message with quick stats
- Active partnerships (cards)
- Pending connections
- Available opportunities
- Recent messages

#### 3.2.3 Application System
**Application Form Fields**:
- Project applying for (auto-filled)
- Relevant experience (textarea)
- Portfolio items related to project
- Proposed approach (textarea)
- Availability to start
- Questions for South Safari (optional)
- Save as draft functionality

#### 3.2.4 Project Management Area
**For approved partnerships, display**:
- Project overview
- Requirements document
- Milestones & progress tracker
- File repository (upload/download)
- Communication thread
- Important dates/deadlines

#### 3.2.5 Messaging System
- Inbox/Sent messages
- Thread-based conversations
- File attachments
- Email notifications for new messages
- Mark as read/unread

### 3.3 Admin Panel

#### 3.3.1 Dashboard
- Summary statistics
- Recent applications
- Active partnerships overview
- Revenue tracking
- Quick actions menu

#### 3.3.2 Project Management
**CRUD operations for projects**:
- Title
- Description (rich text editor)
- Category
- Required technologies
- Partnership model
- Terms preview
- Status (Active/Inactive/Filled)
- Featured (Yes/No)
- Display order

#### 3.3.3 Partnership Collaborator Management
- View all registered partnership collaborators
- Connection status tracking
- Approve/Reject connections
- Assign collaborators to projects
- View collaborator profiles
- Communication history

#### 3.3.4 Content Management
**Editable sections**:
- Homepage content blocks
- Statistics numbers
- About page content
- Terms & Conditions
- Privacy Policy
- Email templates

#### 3.3.5 Communication Center
- Send messages to partnership collaborators
- Email integration
- Notification management
- Message templates
- Bulk messaging (future)

#### 3.3.6 Reports & Export
- Partnership collaborator list export (CSV)
- Project status reports
- Communication logs export
- Partnership agreements export
- Financial tracking (basic)

---

## 4. DATABASE SCHEMA

### 4.1 Core Tables

```sql
-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    country VARCHAR(100),
    phone VARCHAR(50),
    company_name VARCHAR(255),
    portfolio_url VARCHAR(500),
    role ENUM('admin', 'collaborator') DEFAULT 'collaborator',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- Projects table
CREATE TABLE projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE,
    description TEXT,
    requirements TEXT,
    category VARCHAR(100),
    technologies TEXT,
    partnership_model VARCHAR(100),
    terms_preview TEXT,
    status ENUM('active', 'inactive', 'filled') DEFAULT 'active',
    featured BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- Connections table
CREATE TABLE connections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT,
    collaborator_id INT,
    experience TEXT,
    portfolio_items TEXT,
    proposed_approach TEXT,
    availability_date DATE,
    questions TEXT,
    status ENUM('draft', 'submitted', 'reviewing', 'approved', 'rejected') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (collaborator_id) REFERENCES users(id)
);

-- Partnerships table
CREATE TABLE partnerships (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT,
    collaborator_id INT,
    connection_id INT,
    status ENUM('active', 'completed', 'terminated') DEFAULT 'active',
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (collaborator_id) REFERENCES users(id),
    FOREIGN KEY (connection_id) REFERENCES connections(id)
);

-- Messages table
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sender_id INT,
    receiver_id INT,
    partnership_id INT,
    subject VARCHAR(255),
    message TEXT,
    attachments TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (receiver_id) REFERENCES users(id),
    FOREIGN KEY (partnership_id) REFERENCES partnerships(id)
);
```

---

## 5. UI/UX SPECIFICATIONS

### 5.1 Design System
- **Primary Color**: #00B074 (Green - same as Olance)
- **Secondary Color**: #1A1A1A (Dark)
- **Background**: #F5F5F5 (Light Gray)
- **Text**: #333333 (Dark Gray)
- **Success**: #28a745
- **Error**: #dc3545
- **Font**: Inter or similar clean sans-serif

### 5.2 Responsive Breakpoints
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

### 5.3 Component Library
Use Bootstrap 5 components styled to match Olance:
- Cards with subtle shadows
- Rounded buttons
- Clean form inputs
- Modal dialogs
- Dropdown menus
- Progress bars

---

## 6. FUNCTIONAL REQUIREMENTS

### 6.1 Authentication
- Secure password hashing (bcrypt/argon2)
- Session management
- "Remember me" functionality
- Password reset via email
- Login attempt limiting

### 6.2 Email Notifications
**Automated emails for**:
- Registration confirmation
- New application received (to admin)
- Application status updates
- New messages
- Password reset

### 6.3 File Handling
- Support formats: PDF, DOC, DOCX, ZIP
- Max file size: 10MB
- Secure file storage (outside web root)
- Download access control

### 6.4 Search & Filters
- Project title/description search
- Category filtering
- Technology filtering
- Sort by: Date, Title, Status

### 6.5 Data Export
- CSV export functionality
- Include filters in exports
- Secure download links

---

## 7. DEVELOPMENT GUIDELINES FOR AI

### 7.1 Code Structure
```
/south-safari/
├── /assets/
│   ├── /css/
│   ├── /js/
│   └── /images/
├── /includes/
│   ├── config.php
│   ├── db.php
│   └── functions.php
├── /admin/
│   ├── index.php
│   ├── projects.php
│   ├── collaborators.php
│   └── messages.php
├── /collaborator/
│   ├── dashboard.php
│   ├── connections.php
│   └── projects.php
├── /uploads/ (secured)
├── index.php
├── projects.php
├── about.php
├── contact.php
├── login.php
└── register.php
```

### 7.2 Security Requirements
- SQL injection prevention (prepared statements)
- XSS protection (output encoding)
- CSRF tokens on all forms
- Secure session handling
- File upload validation
- Access control on all pages

### 7.3 Development Priorities
1. **Phase 1**: Basic structure, authentication, homepage
2. **Phase 2**: Project CRUD, partnership collaborator registration
3. **Phase 3**: Connection system
4. **Phase 4**: Messaging system
5. **Phase 5**: Admin panel completion
6. **Phase 6**: Polish and testing

### 7.4 AI Coding Instructions
- Use clean, commented code
- Follow PHP PSR standards
- Implement error handling
- Create reusable functions
- Use Bootstrap classes for styling
- Ensure mobile responsiveness
- Test all CRUD operations

---

## 8. DEPLOYMENT NOTES

### 8.1 Server Requirements
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache with mod_rewrite
- PHP extensions: mysqli, mbstring, json, session

### 8.2 Installation Steps
1. Upload files to server
2. Create MySQL database
3. Import database schema
4. Update config.php with database credentials
5. Set proper file permissions
6. Configure email settings

---

## 9. FUTURE ENHANCEMENTS (Post-MVP)

- Multi-language support
- Video calling integration
- Advanced analytics
- Payment integration
- API development
- Mobile app
- One-click installer

---

## 10. SUCCESS CRITERIA

The MVP is considered complete when:
1. Partnership collaborators can browse and connect with partnerships
2. Admin can manage projects and connections
3. Messaging system is functional
4. Platform is mobile-responsive
5. All security measures are implemented
6. Email notifications are working
7. Data export functionality is operational

---

*This PRD is optimized for AI coding agents. Follow the specifications exactly, prioritizing functionality over complex features for the MVP.*