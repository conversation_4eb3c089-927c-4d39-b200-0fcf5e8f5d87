<?php
/**
 * South Safari Partnership Platform - Admin Partnership Opportunities Management
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once '../includes/init.php';

// Require admin authentication
requireAdmin();

$user = getCurrentUser();
$action = $_GET['action'] ?? 'list';
$opportunityId = $_GET['id'] ?? null;

$errors = [];
$success = false;
$opportunity = null;

// Handle different actions
switch ($action) {
    case 'create':
    case 'edit':
        if ($action === 'edit' && $opportunityId) {
            $opportunity = db()->fetchRow(
                "SELECT * FROM partnership_opportunities WHERE id = ?",
                [$opportunityId]
            );
            if (!$opportunity) {
                redirect('opportunities.php', 'Partnership opportunity not found.', 'error');
            }
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
                $errors[] = 'Invalid security token. Please try again.';
            } else {
                // Sanitize and validate input
                $formData = [
                    'title' => sanitizeInput($_POST['title'] ?? ''),
                    'category' => sanitizeInput($_POST['category'] ?? ''),
                    'short_description' => sanitizeInput($_POST['short_description'] ?? ''),
                    'full_description' => sanitizeInput($_POST['full_description'] ?? '', true),
                    'product_features' => array_filter(array_map('sanitizeInput', $_POST['product_features'] ?? [])),
                    'partnership_model' => sanitizeInput($_POST['partnership_model'] ?? ''),
                    'revenue_split' => sanitizeInput($_POST['revenue_split'] ?? ''),
                    'market_opportunity' => sanitizeInput($_POST['market_opportunity'] ?? '', true),
                    'expected_timeline' => sanitizeInput($_POST['expected_timeline'] ?? ''),
                    'partnership_terms' => sanitizeInput($_POST['partnership_terms'] ?? '', true),
                    'requirements' => sanitizeInput($_POST['requirements'] ?? '', true),
                    'status' => sanitizeInput($_POST['status'] ?? 'active'),
                    'featured' => isset($_POST['featured']) ? 1 : 0,
                    'display_order' => (int)($_POST['display_order'] ?? 0)
                ];

                // Validation
                if (empty($formData['title'])) {
                    $errors[] = 'Title is required.';
                }

                if (empty($formData['category'])) {
                    $errors[] = 'Category is required.';
                }

                if (empty($formData['short_description'])) {
                    $errors[] = 'Short description is required.';
                }

                if (empty($formData['full_description'])) {
                    $errors[] = 'Full description is required.';
                }

                if (empty($formData['partnership_model'])) {
                    $errors[] = 'Partnership model is required.';
                }

                // Generate slug
                $slug = generateSlug($formData['title']);
                
                // Check for duplicate slug (excluding current record if editing)
                $slugCheck = "SELECT id FROM partnership_opportunities WHERE slug = ?";
                $slugParams = [$slug];
                if ($action === 'edit' && $opportunityId) {
                    $slugCheck .= " AND id != ?";
                    $slugParams[] = $opportunityId;
                }
                
                if (db()->fetchRow($slugCheck, $slugParams)) {
                    $slug .= '-' . time();
                }

                $formData['slug'] = $slug;
                $formData['product_features'] = json_encode($formData['product_features']);

                if (empty($errors)) {
                    try {
                        if ($action === 'create') {
                            $formData['created_by'] = $user['id'];
                            $newId = db()->insert('partnership_opportunities', $formData);
                            
                            db()->logActivity($user['id'], 'opportunity_created', 'partnership_opportunity', $newId, null, $formData);
                            
                            redirect('opportunities.php', 'Partnership opportunity created successfully!', 'success');
                        } else {
                            $oldData = $opportunity;
                            db()->update('partnership_opportunities', $formData, 'id = ?', [$opportunityId]);
                            
                            db()->logActivity($user['id'], 'opportunity_updated', 'partnership_opportunity', $opportunityId, $oldData, $formData);
                            
                            redirect('opportunities.php', 'Partnership opportunity updated successfully!', 'success');
                        }
                    } catch (Exception $e) {
                        $errors[] = 'Failed to save partnership opportunity. Please try again.';
                        error_log("Opportunity save error: " . $e->getMessage());
                    }
                }
            }
        }
        break;

    case 'delete':
        if ($opportunityId && $_SERVER['REQUEST_METHOD'] === 'POST') {
            if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
                try {
                    $opportunity = db()->fetchRow("SELECT * FROM partnership_opportunities WHERE id = ?", [$opportunityId]);
                    if ($opportunity) {
                        db()->delete('partnership_opportunities', 'id = ?', [$opportunityId]);
                        db()->logActivity($user['id'], 'opportunity_deleted', 'partnership_opportunity', $opportunityId, $opportunity, null);
                        redirect('opportunities.php', 'Partnership opportunity deleted successfully!', 'success');
                    }
                } catch (Exception $e) {
                    redirect('opportunities.php', 'Failed to delete partnership opportunity.', 'error');
                }
            }
        }
        break;

    default:
        // List view
        $page = max(1, (int)($_GET['page'] ?? 1));
        $perPage = 15;
        $offset = ($page - 1) * $perPage;

        $search = sanitizeInput($_GET['search'] ?? '');
        $categoryFilter = sanitizeInput($_GET['category'] ?? '');
        $statusFilter = sanitizeInput($_GET['status'] ?? '');

        $whereClause = "1=1";
        $params = [];

        if ($search) {
            $whereClause .= " AND (title LIKE ? OR short_description LIKE ?)";
            $searchTerm = '%' . db()->escapeLike($search) . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if ($categoryFilter) {
            $whereClause .= " AND category = ?";
            $params[] = $categoryFilter;
        }

        if ($statusFilter) {
            $whereClause .= " AND status = ?";
            $params[] = $statusFilter;
        }

        $totalOpportunities = db()->count('partnership_opportunities', $whereClause, $params);
        $totalPages = ceil($totalOpportunities / $perPage);

        $opportunities = db()->fetchAll(
            "SELECT * FROM partnership_opportunities 
             WHERE {$whereClause} 
             ORDER BY display_order ASC, created_at DESC 
             LIMIT {$perPage} OFFSET {$offset}",
            $params
        );
        break;
}

$pageTitle = $action === 'create' ? 'Create Partnership Opportunity' : 
            ($action === 'edit' ? 'Edit Partnership Opportunity' : 'Partnership Opportunities');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .admin-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .opportunity-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            transition: transform 0.2s;
        }
        
        .opportunity-card:hover {
            transform: translateY(-2px);
        }
        
        .feature-badge {
            background: #E8F5F0;
            color: var(--primary-color);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }
        
        .feature-input {
            margin-bottom: 0.5rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 176, 116, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #008c5a;
            border-color: #008c5a;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-handshake me-2"></i>South Safari Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="opportunities.php">Partnership Opportunities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="collaborators.php">Collaborators</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="connections.php">Connections</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-1"></i><?php echo escape($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <?php displayFlashMessage(); ?>

        <?php if ($action === 'list'): ?>
            <!-- List View -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Partnership Opportunities</h2>
                <a href="opportunities.php?action=create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New Opportunity
                </a>
            </div>

            <!-- Filters -->
            <div class="admin-card">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               placeholder="Search opportunities..." value="<?php echo escape($search); ?>">
                    </div>
                    <div class="col-md-3">
                        <select class="form-control" name="category">
                            <option value="">All Categories</option>
                            <?php foreach (PARTNERSHIP_CATEGORIES as $key => $label): ?>
                                <option value="<?php echo $key; ?>" <?php echo $categoryFilter === $key ? 'selected' : ''; ?>>
                                    <?php echo escape($label); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-control" name="status">
                            <option value="">All Statuses</option>
                            <?php foreach (OPPORTUNITY_STATUSES as $key => $label): ?>
                                <option value="<?php echo $key; ?>" <?php echo $statusFilter === $key ? 'selected' : ''; ?>>
                                    <?php echo escape($label); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                    </div>
                </form>
            </div>

            <!-- Opportunities List -->
            <?php if (empty($opportunities)): ?>
                <div class="admin-card text-center">
                    <i class="fas fa-handshake text-muted fs-1 mb-3"></i>
                    <h5>No Partnership Opportunities Found</h5>
                    <p class="text-muted">Create your first partnership opportunity to get started.</p>
                    <a href="opportunities.php?action=create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Opportunity
                    </a>
                </div>
            <?php else: ?>
                <?php foreach ($opportunities as $opp): ?>
                    <div class="opportunity-card">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="flex-grow-1">
                                <h5 class="mb-1"><?php echo escape($opp['title']); ?></h5>
                                <p class="text-muted mb-2"><?php echo truncate($opp['short_description'], 150); ?></p>
                                <div class="mb-2">
                                    <span class="badge bg-secondary me-2"><?php echo escape($opp['category']); ?></span>
                                    <?php echo getPartnershipModelBadge($opp['partnership_model']); ?>
                                    <?php echo getStatusBadge($opp['status']); ?>
                                    <?php if ($opp['featured']): ?>
                                        <span class="badge bg-warning text-dark ms-1">Featured</span>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-2">
                                    <?php 
                                    $features = json_decode($opp['product_features'], true);
                                    if ($features && is_array($features)) {
                                        foreach (array_slice($features, 0, 3) as $feature) {
                                            echo "<span class='feature-badge'>" . escape($feature) . "</span>";
                                        }
                                        if (count($features) > 3) {
                                            echo "<span class='feature-badge'>+" . (count($features) - 3) . " more</span>";
                                        }
                                    }
                                    ?>
                                </div>
                                <small class="text-muted">
                                    Created <?php echo formatDate($opp['created_at']); ?>
                                    <?php if ($opp['revenue_split']): ?>
                                        • Revenue: <?php echo escape($opp['revenue_split']); ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                            <div class="ms-3">
                                <div class="btn-group">
                                    <a href="opportunities.php?action=edit&id=<?php echo $opp['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="../partnership.php?slug=<?php echo $opp['slug']; ?>" 
                                       class="btn btn-sm btn-outline-info" target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="confirmDelete(<?php echo $opp['id']; ?>, '<?php echo escape($opp['title']); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="d-flex justify-content-center">
                        <?php echo generatePagination($page, $totalPages, 'opportunities.php', [
                            'search' => $search,
                            'category' => $categoryFilter,
                            'status' => $statusFilter
                        ]); ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

        <?php else: ?>
            <!-- Create/Edit Form -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><?php echo $action === 'create' ? 'Create' : 'Edit'; ?> Partnership Opportunity</h2>
                <a href="opportunities.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to List
                </a>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo escape($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="admin-card">
                <form method="POST">
                    <?php echo getCSRFInput(); ?>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label">Title *</label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   value="<?php echo escape($opportunity['title'] ?? ''); ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="category" class="form-label">Category *</label>
                            <select class="form-control" id="category" name="category" required>
                                <option value="">Select Category</option>
                                <?php foreach (PARTNERSHIP_CATEGORIES as $key => $label): ?>
                                    <option value="<?php echo $key; ?>" 
                                            <?php echo ($opportunity['category'] ?? '') === $key ? 'selected' : ''; ?>>
                                        <?php echo escape($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="short_description" class="form-label">Short Description *</label>
                        <textarea class="form-control" id="short_description" name="short_description" 
                                  rows="2" required><?php echo escape($opportunity['short_description'] ?? ''); ?></textarea>
                        <div class="form-text">Brief description for opportunity cards (max 200 characters)</div>
                    </div>

                    <div class="mb-3">
                        <label for="full_description" class="form-label">Full Description *</label>
                        <textarea class="form-control" id="full_description" name="full_description" 
                                  rows="6" required><?php echo escape($opportunity['full_description'] ?? ''); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Product Features</label>
                        <div id="features-container">
                            <?php 
                            $features = [];
                            if ($opportunity && $opportunity['product_features']) {
                                $features = json_decode($opportunity['product_features'], true) ?: [];
                            }
                            
                            if (empty($features)) {
                                $features = [''];
                            }
                            
                            foreach ($features as $index => $feature): ?>
                                <div class="feature-input">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="product_features[]" 
                                               value="<?php echo escape($feature); ?>" placeholder="e.g., Real-time Ordering">
                                        <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addFeature()">
                            <i class="fas fa-plus me-1"></i>Add Feature
                        </button>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="partnership_model" class="form-label">Partnership Model *</label>
                            <select class="form-control" id="partnership_model" name="partnership_model" required>
                                <option value="">Select Model</option>
                                <?php foreach (PARTNERSHIP_MODELS as $key => $label): ?>
                                    <option value="<?php echo $key; ?>" 
                                            <?php echo ($opportunity['partnership_model'] ?? '') === $key ? 'selected' : ''; ?>>
                                        <?php echo escape($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="revenue_split" class="form-label">Revenue Split</label>
                            <input type="text" class="form-control" id="revenue_split" name="revenue_split" 
                                   value="<?php echo escape($opportunity['revenue_split'] ?? ''); ?>" 
                                   placeholder="e.g., 50/50, 60/40">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="market_opportunity" class="form-label">Market Opportunity</label>
                        <textarea class="form-control" id="market_opportunity" name="market_opportunity" 
                                  rows="3"><?php echo escape($opportunity['market_opportunity'] ?? ''); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="expected_timeline" class="form-label">Expected Timeline</label>
                        <input type="text" class="form-control" id="expected_timeline" name="expected_timeline" 
                               value="<?php echo escape($opportunity['expected_timeline'] ?? ''); ?>" 
                               placeholder="e.g., 6-8 months for MVP">
                    </div>

                    <div class="mb-3">
                        <label for="partnership_terms" class="form-label">Partnership Terms</label>
                        <textarea class="form-control" id="partnership_terms" name="partnership_terms" 
                                  rows="3"><?php echo escape($opportunity['partnership_terms'] ?? ''); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="requirements" class="form-label">Requirements</label>
                        <textarea class="form-control" id="requirements" name="requirements" 
                                  rows="3"><?php echo escape($opportunity['requirements'] ?? ''); ?></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <?php foreach (OPPORTUNITY_STATUSES as $key => $label): ?>
                                    <option value="<?php echo $key; ?>" 
                                            <?php echo ($opportunity['status'] ?? 'active') === $key ? 'selected' : ''; ?>>
                                        <?php echo escape($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="display_order" class="form-label">Display Order</label>
                            <input type="number" class="form-control" id="display_order" name="display_order" 
                                   value="<?php echo escape($opportunity['display_order'] ?? 0); ?>" min="0">
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check mt-4">
                                <input type="checkbox" class="form-check-input" id="featured" name="featured" 
                                       <?php echo ($opportunity['featured'] ?? false) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="featured">
                                    Featured Opportunity
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="opportunities.php" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            <?php echo $action === 'create' ? 'Create' : 'Update'; ?> Opportunity
                        </button>
                    </div>
                </form>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the partnership opportunity "<span id="deleteTitle"></span>"?</p>
                    <p class="text-danger small">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" style="display: inline;" id="deleteForm">
                        <?php echo getCSRFInput(); ?>
                        <button type="submit" class="btn btn-danger">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        function addFeature() {
            const container = document.getElementById('features-container');
            const div = document.createElement('div');
            div.className = 'feature-input';
            div.innerHTML = `
                <div class="input-group">
                    <input type="text" class="form-control" name="product_features[]" placeholder="e.g., Real-time Ordering">
                    <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            container.appendChild(div);
        }

        function removeFeature(button) {
            const container = document.getElementById('features-container');
            if (container.children.length > 1) {
                button.closest('.feature-input').remove();
            }
        }

        function confirmDelete(id, title) {
            document.getElementById('deleteTitle').textContent = title;
            document.getElementById('deleteForm').action = 'opportunities.php?action=delete&id=' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
