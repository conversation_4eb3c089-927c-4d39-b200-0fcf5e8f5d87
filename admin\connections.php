<?php
/**
 * South Safari Partnership Platform - Admin Connection Management
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once '../includes/init.php';

// Require admin authentication
requireAdmin();

$user = getCurrentUser();
$action = $_GET['action'] ?? 'list';
$connectionId = $_GET['id'] ?? null;

$errors = [];
$success = false;

// Handle connection review actions
if ($action === 'review' && $connectionId && $_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        $newStatus = sanitizeInput($_POST['status'] ?? '');
        $reviewNotes = sanitizeInput($_POST['review_notes'] ?? '', true);

        if (!in_array($newStatus, ['approved', 'rejected'])) {
            $errors[] = 'Invalid status selected.';
        }

        if (empty($reviewNotes)) {
            $errors[] = 'Review notes are required.';
        }

        if (empty($errors)) {
            try {
                db()->beginTransaction();

                // Get connection details
                $connection = db()->fetchRow(
                    "SELECT pc.*, po.title, u.full_name, u.email 
                     FROM partnership_connections pc 
                     JOIN partnership_opportunities po ON pc.opportunity_id = po.id 
                     JOIN users u ON pc.collaborator_id = u.id 
                     WHERE pc.id = ?",
                    [$connectionId]
                );

                if (!$connection) {
                    throw new Exception('Connection not found.');
                }

                // Update connection
                db()->update('partnership_connections', [
                    'status' => $newStatus,
                    'review_notes' => $reviewNotes,
                    'reviewed_by' => $user['id'],
                    'reviewed_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$connectionId]);

                // Log activity
                db()->logActivity($user['id'], 'connection_reviewed', 'partnership_connection', $connectionId, null, [
                    'status' => $newStatus,
                    'collaborator_name' => $connection['full_name'],
                    'opportunity_title' => $connection['title']
                ]);

                // If approved, create active partnership record
                if ($newStatus === 'approved') {
                    $partnershipData = [
                        'opportunity_id' => $connection['opportunity_id'],
                        'collaborator_id' => $connection['collaborator_id'],
                        'connection_id' => $connectionId,
                        'status' => 'active',
                        'start_date' => date('Y-m-d'),
                        'created_by' => $user['id']
                    ];
                    
                    db()->insert('active_partnerships', $partnershipData);
                }

                db()->commit();

                // Send notification email (would integrate with email system)
                error_log("Connection reviewed: {$connection['full_name']} - {$connection['title']} - Status: {$newStatus}");

                redirect('connections.php', "Connection has been {$newStatus} successfully.", 'success');

            } catch (Exception $e) {
                db()->rollback();
                $errors[] = 'Failed to review connection. Please try again.';
                error_log("Connection review error: " . $e->getMessage());
            }
        }
    }
}

// List view logic
if ($action === 'list') {
    $page = max(1, (int)($_GET['page'] ?? 1));
    $perPage = 20;
    $offset = ($page - 1) * $perPage;

    $statusFilter = sanitizeInput($_GET['status'] ?? '');
    $opportunityFilter = sanitizeInput($_GET['opportunity'] ?? '');

    $whereClause = "1=1";
    $params = [];

    if ($statusFilter) {
        $whereClause .= " AND pc.status = ?";
        $params[] = $statusFilter;
    }

    if ($opportunityFilter) {
        $whereClause .= " AND pc.opportunity_id = ?";
        $params[] = $opportunityFilter;
    }

    $totalConnections = db()->count('partnership_connections pc', $whereClause, $params);
    $totalPages = ceil($totalConnections / $perPage);

    $connections = db()->fetchAll(
        "SELECT pc.*, po.title, po.slug, po.category, u.full_name, u.email, u.company_name,
                reviewer.full_name as reviewer_name
         FROM partnership_connections pc 
         JOIN partnership_opportunities po ON pc.opportunity_id = po.id 
         JOIN users u ON pc.collaborator_id = u.id 
         LEFT JOIN users reviewer ON pc.reviewed_by = reviewer.id
         WHERE {$whereClause} 
         ORDER BY pc.created_at DESC 
         LIMIT {$perPage} OFFSET {$offset}",
        $params
    );

    // Get opportunities for filter
    $opportunities = db()->fetchAll(
        "SELECT DISTINCT po.id, po.title 
         FROM partnership_opportunities po 
         JOIN partnership_connections pc ON po.id = pc.opportunity_id 
         ORDER BY po.title"
    );
}

// Detail view logic
if ($action === 'detail' && $connectionId) {
    $connection = db()->fetchRow(
        "SELECT pc.*, po.title, po.slug, po.category, po.partnership_model, po.revenue_split,
                u.full_name, u.email, u.phone, u.company_name, u.portfolio_url, u.professional_background,
                reviewer.full_name as reviewer_name
         FROM partnership_connections pc 
         JOIN partnership_opportunities po ON pc.opportunity_id = po.id 
         JOIN users u ON pc.collaborator_id = u.id 
         LEFT JOIN users reviewer ON pc.reviewed_by = reviewer.id
         WHERE pc.id = ?",
        [$connectionId]
    );

    if (!$connection) {
        redirect('connections.php', 'Connection not found.', 'error');
    }
}

$pageTitle = $action === 'detail' ? 'Connection Details' : 'Partnership Connections';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .admin-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .connection-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            transition: transform 0.2s;
        }
        
        .connection-card:hover {
            transform: translateY(-2px);
        }
        
        .connection-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-submitted { background: #cce5ff; color: #0066cc; }
        .status-reviewing { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-withdrawn { background: #e2e3e5; color: #383d41; }
        
        .priority-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .priority-high { background: #f8d7da; color: #721c24; }
        .priority-medium { background: #fff3cd; color: #856404; }
        .priority-low { background: #d4edda; color: #155724; }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--secondary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }
        
        .info-item {
            margin-bottom: 1rem;
        }
        
        .info-label {
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 0.25rem;
        }
        
        .info-value {
            color: #666;
            line-height: 1.6;
        }
        
        .review-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            border: 2px solid var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-handshake me-2"></i>South Safari Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="opportunities.php">Partnership Opportunities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="collaborators.php">Collaborators</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="connections.php">Connections</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-1"></i><?php echo escape($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <?php displayFlashMessage(); ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo escape($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- List View -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Partnership Connections</h2>
                <div>
                    <span class="badge bg-primary me-2"><?php echo $totalConnections; ?> Total</span>
                </div>
            </div>

            <!-- Filters -->
            <div class="admin-card">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-control" id="status" name="status">
                            <option value="">All Statuses</option>
                            <?php foreach (CONNECTION_STATUSES as $key => $label): ?>
                                <option value="<?php echo $key; ?>" <?php echo $statusFilter === $key ? 'selected' : ''; ?>>
                                    <?php echo escape($label); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="opportunity" class="form-label">Partnership Opportunity</label>
                        <select class="form-control" id="opportunity" name="opportunity">
                            <option value="">All Opportunities</option>
                            <?php foreach ($opportunities as $opp): ?>
                                <option value="<?php echo $opp['id']; ?>" <?php echo $opportunityFilter == $opp['id'] ? 'selected' : ''; ?>>
                                    <?php echo escape($opp['title']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-1"></i>Filter
                        </button>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <?php if ($statusFilter || $opportunityFilter): ?>
                            <a href="connections.php" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>

            <!-- Connections List -->
            <?php if (empty($connections)): ?>
                <div class="admin-card text-center">
                    <i class="fas fa-handshake text-muted fs-1 mb-3"></i>
                    <h5>No Partnership Connections Found</h5>
                    <p class="text-muted">
                        <?php if ($statusFilter || $opportunityFilter): ?>
                            Try adjusting your filters to see more connections.
                        <?php else: ?>
                            Partnership connections will appear here as collaborators submit them.
                        <?php endif; ?>
                    </p>
                </div>
            <?php else: ?>
                <?php foreach ($connections as $conn): ?>
                    <div class="connection-card">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="flex-grow-1">
                                <h5 class="mb-1"><?php echo escape($conn['full_name']); ?></h5>
                                <p class="text-muted mb-2"><?php echo escape($conn['title']); ?></p>
                                <div class="mb-2">
                                    <span class="badge bg-secondary me-2"><?php echo escape($conn['category']); ?></span>
                                    <?php if ($conn['company_name']): ?>
                                        <span class="badge bg-info"><?php echo escape($conn['company_name']); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="connection-status status-<?php echo $conn['status']; ?>">
                                    <?php echo CONNECTION_STATUSES[$conn['status']] ?? ucfirst($conn['status']); ?>
                                </span>
                                <?php if (in_array($conn['status'], ['submitted', 'reviewing'])): ?>
                                    <div class="mt-2">
                                        <span class="priority-badge priority-medium">Needs Review</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <p class="mb-2">
                                    <strong>Partnership Interest:</strong>
                                    <?php echo truncate($conn['partnership_interest'], 120); ?>
                                </p>
                                <p class="mb-2">
                                    <strong>Proposed Approach:</strong>
                                    <?php echo truncate($conn['proposed_approach'], 120); ?>
                                </p>
                            </div>
                            <div class="col-md-4">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Submitted <?php echo formatDate($conn['submitted_at']); ?>
                                </small><br>
                                <small class="text-muted">
                                    <i class="fas fa-envelope me-1"></i>
                                    <?php echo escape($conn['email']); ?>
                                </small><br>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Available: <?php echo escape(str_replace('_', ' ', ucfirst($conn['availability']))); ?>
                                </small>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <small class="text-muted">
                                Connection #<?php echo str_pad($conn['id'], 6, '0', STR_PAD_LEFT); ?>
                                <?php if ($conn['reviewer_name']): ?>
                                    • Reviewed by <?php echo escape($conn['reviewer_name']); ?>
                                <?php endif; ?>
                            </small>
                            <div>
                                <a href="connections.php?action=detail&id=<?php echo $conn['id']; ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </a>
                                <?php if (in_array($conn['status'], ['submitted', 'reviewing'])): ?>
                                    <a href="connections.php?action=detail&id=<?php echo $conn['id']; ?>#review" class="btn btn-primary btn-sm">
                                        <i class="fas fa-gavel me-1"></i>Review
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="d-flex justify-content-center">
                        <?php echo generatePagination($page, $totalPages, 'connections.php', [
                            'status' => $statusFilter,
                            'opportunity' => $opportunityFilter
                        ]); ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

        <?php elseif ($action === 'detail'): ?>
            <!-- Detail View -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Connection Details</h2>
                <a href="connections.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to List
                </a>
            </div>

            <div class="row">
                <!-- Connection Details -->
                <div class="col-lg-8">
                    <!-- Collaborator Information -->
                    <div class="admin-card">
                        <h3 class="section-title">Collaborator Information</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">Full Name</div>
                                    <div class="info-value"><?php echo escape($connection['full_name']); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Email</div>
                                    <div class="info-value"><?php echo escape($connection['email']); ?></div>
                                </div>
                                <?php if ($connection['phone']): ?>
                                    <div class="info-item">
                                        <div class="info-label">Phone</div>
                                        <div class="info-value"><?php echo escape($connection['phone']); ?></div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <?php if ($connection['company_name']): ?>
                                    <div class="info-item">
                                        <div class="info-label">Company</div>
                                        <div class="info-value"><?php echo escape($connection['company_name']); ?></div>
                                    </div>
                                <?php endif; ?>
                                <?php if ($connection['portfolio_url']): ?>
                                    <div class="info-item">
                                        <div class="info-label">Portfolio</div>
                                        <div class="info-value">
                                            <a href="<?php echo escape($connection['portfolio_url']); ?>" target="_blank">
                                                <?php echo escape($connection['portfolio_url']); ?>
                                            </a>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="info-item">
                                    <div class="info-label">Availability</div>
                                    <div class="info-value"><?php echo escape(str_replace('_', ' ', ucfirst($connection['availability']))); ?></div>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($connection['professional_background']): ?>
                            <div class="info-item">
                                <div class="info-label">Professional Background</div>
                                <div class="info-value"><?php echo nl2br(escape($connection['professional_background'])); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Partnership Opportunity -->
                    <div class="admin-card">
                        <h3 class="section-title">Partnership Opportunity</h3>
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h5><?php echo escape($connection['title']); ?></h5>
                                <span class="badge bg-secondary me-2"><?php echo escape($connection['category']); ?></span>
                                <span class="badge bg-primary">
                                    <?php echo escape($connection['revenue_split'] ?: PARTNERSHIP_MODELS[$connection['partnership_model']]); ?>
                                </span>
                            </div>
                            <a href="../partnership.php?slug=<?php echo $connection['slug']; ?>" target="_blank" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>View Partnership
                            </a>
                        </div>
                    </div>

                    <!-- Connection Request Details -->
                    <div class="admin-card">
                        <h3 class="section-title">Connection Request</h3>
                        
                        <div class="info-item">
                            <div class="info-label">Partnership Interest</div>
                            <div class="info-value"><?php echo nl2br(escape($connection['partnership_interest'])); ?></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Relevant Experience & Expertise</div>
                            <div class="info-value"><?php echo nl2br(escape($connection['relevant_experience'])); ?></div>
                        </div>

                        <?php if ($connection['portfolio_items']): ?>
                            <div class="info-item">
                                <div class="info-label">Portfolio & Previous Work</div>
                                <div class="info-value"><?php echo nl2br(escape($connection['portfolio_items'])); ?></div>
                            </div>
                        <?php endif; ?>

                        <div class="info-item">
                            <div class="info-label">Proposed Partnership Approach</div>
                            <div class="info-value"><?php echo nl2br(escape($connection['proposed_approach'])); ?></div>
                        </div>

                        <?php if ($connection['additional_notes']): ?>
                            <div class="info-item">
                                <div class="info-label">Additional Notes</div>
                                <div class="info-value"><?php echo nl2br(escape($connection['additional_notes'])); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Review Section -->
                    <?php if (in_array($connection['status'], ['submitted', 'reviewing'])): ?>
                        <div class="admin-card" id="review">
                            <h3 class="section-title">Review Connection</h3>
                            <div class="review-form">
                                <form method="POST">
                                    <?php echo getCSRFInput(); ?>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Decision *</label>
                                        <select class="form-control" id="status" name="status" required>
                                            <option value="">Select Decision</option>
                                            <option value="approved">Approve Connection</option>
                                            <option value="rejected">Reject Connection</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="review_notes" class="form-label">Review Notes *</label>
                                        <textarea class="form-control" id="review_notes" name="review_notes" 
                                                  rows="4" required
                                                  placeholder="Provide detailed feedback about your decision..."></textarea>
                                        <div class="form-text">These notes will be shared with the collaborator.</div>
                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                                            Cancel
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-gavel me-2"></i>Submit Review
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php elseif ($connection['review_notes']): ?>
                        <!-- Existing Review -->
                        <div class="admin-card">
                            <h3 class="section-title">Review Notes</h3>
                            <div class="info-value"><?php echo nl2br(escape($connection['review_notes'])); ?></div>
                            <small class="text-muted">
                                Reviewed by <?php echo escape($connection['reviewer_name']); ?>
                                on <?php echo formatDateTime($connection['reviewed_at']); ?>
                            </small>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Status Card -->
                    <div class="admin-card">
                        <h5 class="mb-3">Connection Status</h5>
                        <div class="text-center mb-3">
                            <span class="connection-status status-<?php echo $connection['status']; ?>" style="font-size: 1rem; padding: 0.5rem 1rem;">
                                <?php echo CONNECTION_STATUSES[$connection['status']] ?? ucfirst($connection['status']); ?>
                            </span>
                        </div>
                        <div class="small text-muted">
                            <div class="mb-2">
                                <strong>Submitted:</strong> <?php echo formatDateTime($connection['submitted_at']); ?>
                            </div>
                            <?php if ($connection['reviewed_at']): ?>
                                <div class="mb-2">
                                    <strong>Reviewed:</strong> <?php echo formatDateTime($connection['reviewed_at']); ?>
                                </div>
                            <?php endif; ?>
                            <div>
                                <strong>Connection ID:</strong> #<?php echo str_pad($connection['id'], 6, '0', STR_PAD_LEFT); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="admin-card">
                        <h6 class="mb-3">Quick Actions</h6>
                        <div class="d-grid gap-2">
                            <a href="connections.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>Back to Connections
                            </a>
                            <a href="../partnership.php?slug=<?php echo $connection['slug']; ?>" target="_blank" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-external-link-alt me-2"></i>View Partnership
                            </a>
                            <a href="collaborator-details.php?id=<?php echo $connection['collaborator_id']; ?>" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-user me-2"></i>View Collaborator
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
