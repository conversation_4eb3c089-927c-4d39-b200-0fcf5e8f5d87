<?php
/**
 * South Safari Partnership Platform - Comprehensive Test Suite
 * Tests all critical functionality and reports status
 */

define('SS_DIRECT_ACCESS', true);

echo "=== SOUTH SAFARI PARTNERSHIP PLATFORM - COMPREHENSIVE TEST ===\n";
echo "Testing Phase 2 Implementation - Partnership Connection System\n";
echo "================================================================\n\n";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    $totalTests++;
    
    echo "Testing: {$testName}... ";
    
    try {
        $result = $testFunction();
        if ($result === true) {
            echo "✅ PASS\n";
            $testResults[$testName] = 'PASS';
            $passedTests++;
        } else {
            echo "❌ FAIL: {$result}\n";
            $testResults[$testName] = "FAIL: {$result}";
        }
    } catch (Exception $e) {
        echo "❌ ERROR: " . $e->getMessage() . "\n";
        $testResults[$testName] = "ERROR: " . $e->getMessage();
    }
}

// Test 1: Configuration Loading
runTest("Configuration Loading", function() {
    require_once 'includes/config.php';
    return defined('DB_NAME') && defined('SS_NAME');
});

// Test 2: Database Connection
runTest("Database Connection", function() {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    return $pdo !== null;
});

// Test 3: Database Tables
runTest("Database Tables", function() {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $requiredTables = ['users', 'partnership_opportunities', 'partnership_connections', 'active_partnerships'];
    foreach ($requiredTables as $table) {
        if (!in_array($table, $tables)) {
            return "Missing table: {$table}";
        }
    }
    return true;
});

// Test 4: Sample Data
runTest("Sample Partnership Opportunities", function() {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    $count = $pdo->query("SELECT COUNT(*) FROM partnership_opportunities WHERE status = 'active'")->fetchColumn();
    return $count > 0 ? true : "No active partnership opportunities found";
});

// Test 5: Admin User
runTest("Admin User Exists", function() {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    $count = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'")->fetchColumn();
    return $count > 0 ? true : "No admin user found";
});

// Test 6: Initialization System
runTest("System Initialization", function() {
    require_once 'includes/init.php';
    return function_exists('getPartnershipOpportunities');
});

// Test 7: Partnership Functions
runTest("Partnership Functions", function() {
    $opportunities = getPartnershipOpportunities(1);
    return is_array($opportunities);
});

// Test 8: Partnership Statistics
runTest("Partnership Statistics", function() {
    $stats = getPartnershipStats();
    return is_array($stats) && isset($stats['total_opportunities']);
});

// Test 9: Security Functions
runTest("Security Functions", function() {
    return function_exists('generateCSRFToken') && function_exists('validateCSRFToken');
});

// Test 10: Authentication Functions
runTest("Authentication Functions", function() {
    return function_exists('isLoggedIn') && function_exists('getCurrentUser');
});

// Test 11: File Structure
runTest("Critical Files Exist", function() {
    $criticalFiles = [
        'index.php',
        'partnerships.php', 
        'partnership.php',
        'connect.php',
        'register.php',
        'login.php',
        'admin/index.php',
        'admin/opportunities.php',
        'admin/connections.php',
        'collaborator/index.php',
        'collaborator/connections.php'
    ];
    
    foreach ($criticalFiles as $file) {
        if (!file_exists($file)) {
            return "Missing file: {$file}";
        }
    }
    return true;
});

// Test 12: Directory Permissions
runTest("Directory Permissions", function() {
    $directories = ['uploads', 'logs'];
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            return "Missing directory: {$dir}";
        }
        if (!is_writable($dir)) {
            return "Directory not writable: {$dir}";
        }
    }
    return true;
});

// Test 13: Constants Defined
runTest("Required Constants", function() {
    $constants = ['PARTNERSHIP_CATEGORIES', 'PARTNERSHIP_MODELS', 'CONNECTION_STATUSES', 'SUCCESS_MESSAGES'];
    foreach ($constants as $constant) {
        if (!defined($constant)) {
            return "Missing constant: {$constant}";
        }
    }
    return true;
});

// Test 14: Page Loading (Basic)
runTest("Homepage File Structure", function() {
    // Test that the homepage file exists and has the right content structure
    $content = file_get_contents('index.php');
    return strpos($content, 'South Safari') !== false && strpos($content, 'Partnership') !== false;
});

// Test 15: Database Schema Integrity
runTest("Database Schema Integrity", function() {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    
    // Check partnership_opportunities table structure
    $columns = $pdo->query("DESCRIBE partnership_opportunities")->fetchAll(PDO::FETCH_COLUMN);
    $requiredColumns = ['id', 'title', 'slug', 'category', 'partnership_model', 'product_features'];
    foreach ($requiredColumns as $column) {
        if (!in_array($column, $columns)) {
            return "Missing column in partnership_opportunities: {$column}";
        }
    }
    return true;
});

echo "\n=== TEST SUMMARY ===\n";
echo "Total Tests: {$totalTests}\n";
echo "Passed: {$passedTests}\n";
echo "Failed: " . ($totalTests - $passedTests) . "\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

if ($passedTests === $totalTests) {
    echo "🎉 ALL TESTS PASSED! South Safari Partnership Platform is fully functional.\n\n";
    echo "✅ READY FOR PRODUCTION USE\n";
    echo "✅ Phase 2 Implementation Complete\n";
    echo "✅ Partnership Connection System Operational\n\n";
    
    echo "=== QUICK START GUIDE ===\n";
    echo "1. Visit: http://localhost/south-safari-v3/\n";
    echo "2. Admin Login: <EMAIL> / admin123\n";
    echo "3. Test Registration: http://localhost/south-safari-v3/register.php\n";
    echo "4. Browse Partnerships: http://localhost/south-safari-v3/partnerships.php\n";
    echo "5. Admin Panel: http://localhost/south-safari-v3/admin/\n\n";
} else {
    echo "⚠️  SOME TESTS FAILED - Review failed tests above\n\n";
    echo "Failed Tests:\n";
    foreach ($testResults as $test => $result) {
        if (strpos($result, 'FAIL') === 0 || strpos($result, 'ERROR') === 0) {
            echo "- {$test}: {$result}\n";
        }
    }
}

echo "=== END OF COMPREHENSIVE TEST ===\n";
?>
