<?php
/**
 * Load sample data into the database
 */

define('SS_DIRECT_ACCESS', true);
require_once 'includes/config.php';

try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    
    echo "Loading sample data...\n";
    
    // Read and execute sample data SQL
    $sql = file_get_contents('database/sample_data.sql');
    
    // Split by semicolons and execute each statement
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            $pdo->exec($statement);
        }
    }
    
    echo "Sample data loaded successfully!\n";
    
    // Verify data was loaded
    $count = $pdo->query("SELECT COUNT(*) FROM partnership_opportunities WHERE status = 'active'")->fetchColumn();
    echo "Active partnership opportunities: {$count}\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
