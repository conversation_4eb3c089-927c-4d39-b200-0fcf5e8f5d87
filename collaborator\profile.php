<?php
/**
 * South Safari Partnership Platform - Collaborator Profile Management
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once '../includes/init.php';

// Require collaborator authentication
requireAuth();
if (!isCollaborator() && !isAdmin()) {
    redirect('../login.php', 'Access denied. Please sign in with a partnership collaborator account.', 'error');
}

$user = getCurrentUser();
$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Sanitize input
        $formData = [
            'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
            'country' => sanitizeInput($_POST['country'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'company_name' => sanitizeInput($_POST['company_name'] ?? ''),
            'portfolio_url' => sanitizeInput($_POST['portfolio_url'] ?? ''),
            'professional_background' => sanitizeInput($_POST['professional_background'] ?? '', true)
        ];

        // Validation
        if (empty($formData['full_name'])) {
            $errors[] = 'Full name is required.';
        }

        if (!empty($formData['portfolio_url']) && !isValidUrl($formData['portfolio_url'])) {
            $errors[] = 'Please enter a valid portfolio URL.';
        }

        // If no errors, update profile
        if (empty($errors)) {
            try {
                $oldData = $user;
                
                db()->update('users', $formData, 'id = ?', [$user['id']]);
                
                db()->logActivity($user['id'], 'profile_updated', 'user', $user['id'], $oldData, $formData);
                
                $success = true;
                
                // Reload user data
                $user = db()->fetchRow("SELECT * FROM users WHERE id = ?", [$user['id']]);
                
            } catch (Exception $e) {
                $errors[] = 'Failed to update profile. Please try again.';
                error_log("Profile update error: " . $e->getMessage());
            }
        }
    }
}

$pageTitle = 'My Profile';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .profile-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .profile-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 176, 116, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #008c5a;
            border-color: #008c5a;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--secondary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .stats-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-handshake me-2"></i>South Safari
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="connections.php">My Connections</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../partnerships.php">Browse Opportunities</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i><?php echo escape($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <div class="profile-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">My Partnership Profile</h1>
                    <p class="mb-0">Manage your profile information and partnership preferences</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="profile-avatar">
                        <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-4">
        <?php displayFlashMessage(); ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i><?php echo SUCCESS_MESSAGES['profile_updated']; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo escape($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Form -->
            <div class="col-lg-8">
                <div class="profile-card">
                    <h3 class="section-title">Profile Information</h3>
                    
                    <form method="POST">
                        <?php echo getCSRFInput(); ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo escape($user['full_name']); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" 
                                       value="<?php echo escape($user['email']); ?>" disabled>
                                <div class="form-text">Email cannot be changed. Contact support if needed.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Country</label>
                                <select class="form-control" id="country" name="country">
                                    <option value="">Select Country</option>
                                    <option value="South Africa" <?php echo $user['country'] === 'South Africa' ? 'selected' : ''; ?>>South Africa</option>
                                    <option value="Botswana" <?php echo $user['country'] === 'Botswana' ? 'selected' : ''; ?>>Botswana</option>
                                    <option value="Namibia" <?php echo $user['country'] === 'Namibia' ? 'selected' : ''; ?>>Namibia</option>
                                    <option value="Zimbabwe" <?php echo $user['country'] === 'Zimbabwe' ? 'selected' : ''; ?>>Zimbabwe</option>
                                    <option value="Zambia" <?php echo $user['country'] === 'Zambia' ? 'selected' : ''; ?>>Zambia</option>
                                    <option value="Other" <?php echo $user['country'] === 'Other' ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo escape($user['phone']); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="company_name" class="form-label">Company/Organization</label>
                            <input type="text" class="form-control" id="company_name" name="company_name" 
                                   value="<?php echo escape($user['company_name']); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="portfolio_url" class="form-label">Portfolio/Website URL</label>
                            <input type="url" class="form-control" id="portfolio_url" name="portfolio_url" 
                                   value="<?php echo escape($user['portfolio_url']); ?>"
                                   placeholder="https://your-portfolio.com">
                        </div>

                        <div class="mb-3">
                            <label for="professional_background" class="form-label">Professional Background</label>
                            <textarea class="form-control" id="professional_background" name="professional_background" 
                                      rows="4" placeholder="Brief description of your professional experience and expertise..."><?php echo escape($user['professional_background']); ?></textarea>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Account Information -->
                <div class="profile-card">
                    <h5 class="mb-3">Account Information</h5>
                    <div class="mb-3">
                        <small class="text-muted">Member Since</small>
                        <div><?php echo formatDate($user['created_at']); ?></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Login</small>
                        <div><?php echo $user['last_login'] ? formatDateTime($user['last_login']) : 'Never'; ?></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Account Status</small>
                        <div>
                            <span class="badge bg-success">Active</span>
                            <?php if ($user['email_verified']): ?>
                                <span class="badge bg-info ms-1">Verified</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Partnership Statistics -->
                <?php 
                $userStats = [
                    'connections' => db()->count('partnership_connections', 'collaborator_id = ?', [$user['id']]),
                    'approved' => db()->count('partnership_connections', 'collaborator_id = ? AND status = ?', [$user['id'], 'approved']),
                    'active_partnerships' => db()->count('active_partnerships', 'collaborator_id = ? AND status = ?', [$user['id'], 'active'])
                ];
                ?>
                
                <div class="profile-card">
                    <h5 class="mb-3">Partnership Statistics</h5>
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="stats-card">
                                <div class="stat-number"><?php echo $userStats['connections']; ?></div>
                                <div class="stat-label">Total Connections</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stat-number"><?php echo $userStats['approved']; ?></div>
                                <div class="stat-label">Approved</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stat-number"><?php echo $userStats['active_partnerships']; ?></div>
                                <div class="stat-label">Active</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="profile-card">
                    <h6 class="mb-3">Quick Actions</h6>
                    <div class="d-grid gap-2">
                        <a href="connections.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-handshake me-2"></i>View My Connections
                        </a>
                        <a href="../partnerships.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-search me-2"></i>Browse Partnerships
                        </a>
                        <a href="#" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-key me-2"></i>Change Password
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
