<?php
/**
 * South Safari Partnership Platform - Authentication & Authorization
 * Version 2.0 - Partnership-Centric & Feature-Focused
 * Created: 2025-01-18
 */

// Prevent direct access
if (!defined('SS_INIT')) {
    die('Direct access not permitted');
}

class Auth {
    private static $instance = null;
    private $db;
    private $currentUser = null;

    private function __construct() {
        $this->db = Database::getInstance();
        $this->initSession();
        $this->loadCurrentUser();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize secure session
     */
    private function initSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Regenerate session ID periodically for security
        if (!isset($_SESSION['last_regeneration'])) {
            $this->regenerateSession();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            $this->regenerateSession();
        }
    }

    /**
     * Regenerate session ID
     */
    private function regenerateSession() {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }

    /**
     * Load current user from session
     */
    private function loadCurrentUser() {
        if (isset($_SESSION['user_id'])) {
            $this->currentUser = $this->db->fetchRow(
                "SELECT * FROM users WHERE id = ? AND status = 'active'",
                [$_SESSION['user_id']]
            );
            
            if (!$this->currentUser) {
                $this->logout();
            }
        }
    }

    /**
     * Register a new partnership collaborator
     */
    public function register($email, $password, $fullName, $country = null, $phone = null, $companyName = null, $portfolioUrl = null, $professionalBackground = null) {
        // Check if email already exists
        if ($this->db->exists('users', 'email = ?', [$email])) {
            throw new Exception(ERROR_MESSAGES['email_exists']);
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email format.');
        }

        // Validate password strength
        if (strlen($password) < 8) {
            throw new Exception('Password must be at least 8 characters long.');
        }

        // Hash password
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        // Generate email verification token
        $verificationToken = $this->generateToken();

        // Prepare user data
        $userData = [
            'email' => strtolower(trim($email)),
            'password_hash' => $passwordHash,
            'full_name' => trim($fullName),
            'country' => $country ? trim($country) : null,
            'phone' => $phone ? trim($phone) : null,
            'company_name' => $companyName ? trim($companyName) : null,
            'portfolio_url' => $portfolioUrl ? trim($portfolioUrl) : null,
            'professional_background' => $professionalBackground ? trim($professionalBackground) : null,
            'role' => 'collaborator',
            'status' => 'active',
            'email_verified' => REQUIRE_EMAIL_VERIFICATION ? false : true,
            'email_verification_token' => REQUIRE_EMAIL_VERIFICATION ? $verificationToken : null
        ];

        try {
            $this->db->beginTransaction();
            
            $userId = $this->db->insert('users', $userData);
            
            // Log registration activity
            $this->db->logActivity($userId, 'user_registered', 'user', $userId, null, [
                'email' => $email,
                'role' => 'collaborator'
            ]);
            
            $this->db->commit();
            
            // Send verification email if required
            if (REQUIRE_EMAIL_VERIFICATION) {
                $this->sendVerificationEmail($email, $fullName, $verificationToken);
            }
            
            return [
                'success' => true,
                'user_id' => $userId,
                'requires_verification' => REQUIRE_EMAIL_VERIFICATION
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Registration failed: " . $e->getMessage());
            throw new Exception('Registration failed. Please try again.');
        }
    }

    /**
     * Login user
     */
    public function login($email, $password, $rememberMe = false) {
        $user = $this->db->fetchRow(
            "SELECT * FROM users WHERE email = ?",
            [strtolower(trim($email))]
        );

        if (!$user || !password_verify($password, $user['password_hash'])) {
            // Log failed login attempt
            $this->logFailedLogin($email);
            throw new Exception(ERROR_MESSAGES['invalid_credentials']);
        }

        // Check if email is verified
        if (REQUIRE_EMAIL_VERIFICATION && !$user['email_verified']) {
            throw new Exception(ERROR_MESSAGES['email_not_verified']);
        }

        // Check account status
        if ($user['status'] !== 'active') {
            throw new Exception(ERROR_MESSAGES['account_suspended']);
        }

        // Update last login
        $this->db->update('users', 
            ['last_login' => date('Y-m-d H:i:s')], 
            'id = ?', 
            [$user['id']]
        );

        // Set session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['full_name'];
        $_SESSION['login_time'] = time();

        // Handle remember me
        if ($rememberMe) {
            $this->setRememberMeCookie($user['id']);
        }

        // Log successful login
        $this->db->logActivity($user['id'], 'user_login', 'user', $user['id']);

        $this->currentUser = $user;
        
        return [
            'success' => true,
            'user' => $this->sanitizeUserData($user),
            'redirect_url' => $this->getRedirectUrl($user['role'])
        ];
    }

    /**
     * Logout user
     */
    public function logout() {
        if ($this->currentUser) {
            $this->db->logActivity($this->currentUser['id'], 'user_logout', 'user', $this->currentUser['id']);
        }

        // Clear remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/', '', true, true);
        }

        // Destroy session
        session_destroy();
        $this->currentUser = null;
        
        return true;
    }

    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        return $this->currentUser !== null;
    }

    /**
     * Get current user
     */
    public function getCurrentUser() {
        return $this->currentUser;
    }

    /**
     * Check if user has specific role
     */
    public function hasRole($role) {
        return $this->currentUser && $this->currentUser['role'] === $role;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin() {
        return $this->hasRole('admin');
    }

    /**
     * Check if user is collaborator
     */
    public function isCollaborator() {
        return $this->hasRole('collaborator');
    }

    /**
     * Require authentication
     */
    public function requireAuth($redirectUrl = null) {
        if (!$this->isLoggedIn()) {
            if ($redirectUrl) {
                $_SESSION['redirect_after_login'] = $redirectUrl;
            }
            header('Location: ' . BASE_URL . 'login.php');
            exit;
        }
    }

    /**
     * Require specific role
     */
    public function requireRole($role, $redirectUrl = null) {
        $this->requireAuth($redirectUrl);
        
        if (!$this->hasRole($role)) {
            throw new Exception(ERROR_MESSAGES['access_denied']);
        }
    }

    /**
     * Require admin access
     */
    public function requireAdmin() {
        $this->requireRole('admin');
    }

    /**
     * Generate secure token
     */
    private function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }

    /**
     * Send verification email
     */
    private function sendVerificationEmail($email, $name, $token) {
        $verificationUrl = BASE_URL . "verify-email.php?token=" . $token;
        
        // This would integrate with your email system
        // For now, we'll log it
        error_log("Verification email for {$email}: {$verificationUrl}");
        
        return true;
    }

    /**
     * Verify email address
     */
    public function verifyEmail($token) {
        $user = $this->db->fetchRow(
            "SELECT * FROM users WHERE email_verification_token = ? AND email_verified = 0",
            [$token]
        );

        if (!$user) {
            throw new Exception('Invalid or expired verification token.');
        }

        $this->db->update('users', 
            [
                'email_verified' => 1,
                'email_verification_token' => null
            ], 
            'id = ?', 
            [$user['id']]
        );

        $this->db->logActivity($user['id'], 'email_verified', 'user', $user['id']);

        return true;
    }

    /**
     * Request password reset
     */
    public function requestPasswordReset($email) {
        $user = $this->db->fetchRow(
            "SELECT * FROM users WHERE email = ? AND status = 'active'",
            [strtolower(trim($email))]
        );

        if (!$user) {
            // Don't reveal if email exists or not
            return true;
        }

        $resetToken = $this->generateToken();
        $resetExpires = date('Y-m-d H:i:s', time() + PASSWORD_RESET_EXPIRY);

        $this->db->update('users', 
            [
                'password_reset_token' => $resetToken,
                'password_reset_expires' => $resetExpires
            ], 
            'id = ?', 
            [$user['id']]
        );

        // Send reset email (would integrate with email system)
        $resetUrl = BASE_URL . "reset-password.php?token=" . $resetToken;
        error_log("Password reset for {$email}: {$resetUrl}");

        return true;
    }

    /**
     * Reset password with token
     */
    public function resetPassword($token, $newPassword) {
        $user = $this->db->fetchRow(
            "SELECT * FROM users WHERE password_reset_token = ? AND password_reset_expires > NOW()",
            [$token]
        );

        if (!$user) {
            throw new Exception('Invalid or expired reset token.');
        }

        if (strlen($newPassword) < 8) {
            throw new Exception('Password must be at least 8 characters long.');
        }

        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);

        $this->db->update('users', 
            [
                'password_hash' => $passwordHash,
                'password_reset_token' => null,
                'password_reset_expires' => null
            ], 
            'id = ?', 
            [$user['id']]
        );

        $this->db->logActivity($user['id'], 'password_reset', 'user', $user['id']);

        return true;
    }

    /**
     * Get redirect URL based on role
     */
    private function getRedirectUrl($role) {
        switch ($role) {
            case 'admin':
                return ADMIN_URL;
            case 'collaborator':
                return COLLABORATOR_URL;
            default:
                return BASE_URL;
        }
    }

    /**
     * Sanitize user data for output
     */
    private function sanitizeUserData($user) {
        unset($user['password_hash']);
        unset($user['email_verification_token']);
        unset($user['password_reset_token']);
        unset($user['password_reset_expires']);
        return $user;
    }

    /**
     * Log failed login attempt
     */
    private function logFailedLogin($email) {
        $this->db->logActivity(null, 'login_failed', 'user', null, null, [
            'email' => $email,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ]);
    }

    /**
     * Set remember me cookie
     */
    private function setRememberMeCookie($userId) {
        $token = $this->generateToken();
        $expires = time() + SESSION_LIFETIME;
        
        setcookie('remember_token', $token, $expires, '/', '', true, true);
        
        // Store token in database (you might want to create a separate table for this)
        $this->db->update('users', 
            ['remember_token' => password_hash($token, PASSWORD_DEFAULT)], 
            'id = ?', 
            [$userId]
        );
    }
}

// Global auth helper functions
function auth() {
    return Auth::getInstance();
}

function isLoggedIn() {
    return auth()->isLoggedIn();
}

function getCurrentUser() {
    return auth()->getCurrentUser();
}

function requireAuth($redirectUrl = null) {
    return auth()->requireAuth($redirectUrl);
}

function requireAdmin() {
    return auth()->requireAdmin();
}

function isAdmin() {
    return auth()->isAdmin();
}

function isCollaborator() {
    return auth()->isCollaborator();
}
