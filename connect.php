<?php
/**
 * South Safari Partnership Platform - Partnership Connection Form
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once 'includes/init.php';

// Require authentication
requireAuth();

$user = getCurrentUser();
$opportunityId = (int)($_GET['opportunity'] ?? 0);

// Get partnership opportunity
$opportunity = db()->fetchRow(
    "SELECT * FROM partnership_opportunities WHERE id = ? AND status = 'active'",
    [$opportunityId]
);

if (!$opportunity) {
    redirect('partnerships.php', 'Partnership opportunity not found.', 'error');
}

// Check if user already has a connection to this opportunity
if (hasConnectionToOpportunity($opportunityId, $user['id'])) {
    redirect("partnership.php?slug={$opportunity['slug']}", 'You have already submitted a connection for this partnership opportunity.', 'info');
}

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Sanitize and validate input
        $formData = [
            'relevant_experience' => sanitizeInput($_POST['relevant_experience'] ?? '', true),
            'portfolio_items' => sanitizeInput($_POST['portfolio_items'] ?? '', true),
            'proposed_approach' => sanitizeInput($_POST['proposed_approach'] ?? '', true),
            'availability' => sanitizeInput($_POST['availability'] ?? ''),
            'additional_notes' => sanitizeInput($_POST['additional_notes'] ?? '', true),
            'partnership_interest' => sanitizeInput($_POST['partnership_interest'] ?? '', true)
        ];

        // Validation
        if (empty($formData['relevant_experience'])) {
            $errors[] = 'Please describe your relevant experience.';
        }

        if (empty($formData['proposed_approach'])) {
            $errors[] = 'Please describe your proposed approach to this partnership.';
        }

        if (empty($formData['availability'])) {
            $errors[] = 'Please indicate your availability.';
        }

        if (empty($formData['partnership_interest'])) {
            $errors[] = 'Please explain your interest in this partnership.';
        }

        // Check rate limiting
        if (!checkRateLimit('connection_submit', $user['id'], 3, 3600)) {
            $errors[] = 'Too many connection submissions. Please wait before submitting another.';
        }

        // If no errors, create connection
        if (empty($errors)) {
            try {
                db()->beginTransaction();

                // Prepare connection data
                $connectionData = array_merge($formData, [
                    'opportunity_id' => $opportunityId,
                    'collaborator_id' => $user['id'],
                    'status' => 'submitted',
                    'submitted_at' => date('Y-m-d H:i:s')
                ]);

                $connectionId = db()->insert('partnership_connections', $connectionData);

                // Log activity
                db()->logActivity($user['id'], 'connection_submitted', 'partnership_connection', $connectionId, null, [
                    'opportunity_id' => $opportunityId,
                    'opportunity_title' => $opportunity['title']
                ]);

                db()->commit();

                // Send notification email (would integrate with email system)
                error_log("Partnership connection submitted: User {$user['id']} connected to opportunity {$opportunityId}");

                redirect("partnership.php?slug={$opportunity['slug']}", SUCCESS_MESSAGES['connection_submitted'], 'success');

            } catch (Exception $e) {
                db()->rollback();
                $errors[] = 'Failed to submit connection. Please try again.';
                error_log("Connection submission error: " . $e->getMessage());
            }
        }
    }
}

$pageTitle = "Connect with {$opportunity['title']}";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .connect-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            color: white;
            padding: 3rem 0;
        }
        
        .form-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }
        
        .opportunity-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 176, 116, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            background-color: #008c5a;
            border-color: #008c5a;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--secondary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }
        
        .form-help {
            background: #e8f5f0;
            border-left: 4px solid var(--primary-color);
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-radius: 0 5px 5px 0;
        }
        
        .feature-badge {
            background: #E8F5F0;
            color: var(--primary-color);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }
        
        .partnership-highlight {
            background: var(--primary-color);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }
        
        .char-counter {
            font-size: 0.8rem;
            color: #6c757d;
            text-align: right;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-handshake me-2"></i>South Safari
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="partnerships.php">Partnerships</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i><?php echo escape($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="<?php echo $user['role'] === 'admin' ? 'admin/' : 'collaborator/'; ?>">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="connect-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-6 fw-bold mb-3">Connect with Partnership Opportunity</h1>
                    <p class="lead mb-0">Submit your connection request to start a meaningful partnership collaboration</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <i class="fas fa-handshake" style="font-size: 4rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </section>

    <div class="container my-5">
        <?php displayFlashMessage(); ?>

        <div class="row">
            <!-- Main Form -->
            <div class="col-lg-8">
                <!-- Opportunity Summary -->
                <div class="opportunity-summary">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h4 class="mb-2"><?php echo escape($opportunity['title']); ?></h4>
                            <span class="badge bg-primary"><?php echo escape($opportunity['category']); ?></span>
                        </div>
                        <a href="partnership.php?slug=<?php echo $opportunity['slug']; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>View Full Details
                        </a>
                    </div>
                    <p class="text-muted mb-3"><?php echo escape($opportunity['short_description']); ?></p>
                    <div class="mb-2">
                        <?php 
                        $features = json_decode($opportunity['product_features'], true);
                        if ($features && is_array($features)) {
                            foreach (array_slice($features, 0, 5) as $feature) {
                                echo "<span class='feature-badge'>" . escape($feature) . "</span>";
                            }
                        }
                        ?>
                    </div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo escape($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <!-- Connection Form -->
                <div class="form-card">
                    <h3 class="section-title">Partnership Connection Request</h3>
                    
                    <div class="form-help">
                        <h6><i class="fas fa-lightbulb me-2"></i>Connection Tips</h6>
                        <p class="mb-0">Be specific about your experience and approach. Show how your expertise aligns with this partnership opportunity and how you envision the collaboration working.</p>
                    </div>

                    <form method="POST" id="connectionForm">
                        <?php echo getCSRFInput(); ?>

                        <!-- Partnership Interest -->
                        <div class="mb-4">
                            <label for="partnership_interest" class="form-label">
                                <strong>Why are you interested in this partnership? *</strong>
                            </label>
                            <textarea class="form-control" id="partnership_interest" name="partnership_interest" 
                                      rows="4" required maxlength="1000"
                                      placeholder="Explain what attracts you to this partnership opportunity and how it aligns with your goals..."><?php echo escape($formData['partnership_interest'] ?? ''); ?></textarea>
                            <div class="char-counter">
                                <span id="interest-count">0</span>/1000 characters
                            </div>
                        </div>

                        <!-- Relevant Experience -->
                        <div class="mb-4">
                            <label for="relevant_experience" class="form-label">
                                <strong>Relevant Experience & Expertise *</strong>
                            </label>
                            <textarea class="form-control" id="relevant_experience" name="relevant_experience" 
                                      rows="5" required maxlength="2000"
                                      placeholder="Describe your relevant experience, skills, and expertise that make you a great fit for this partnership..."><?php echo escape($formData['relevant_experience'] ?? ''); ?></textarea>
                            <div class="char-counter">
                                <span id="experience-count">0</span>/2000 characters
                            </div>
                        </div>

                        <!-- Portfolio Items -->
                        <div class="mb-4">
                            <label for="portfolio_items" class="form-label">
                                <strong>Portfolio & Previous Work</strong>
                            </label>
                            <textarea class="form-control" id="portfolio_items" name="portfolio_items" 
                                      rows="4" maxlength="1500"
                                      placeholder="Share examples of your previous work, projects, or achievements that demonstrate your capabilities..."><?php echo escape($formData['portfolio_items'] ?? ''); ?></textarea>
                            <div class="char-counter">
                                <span id="portfolio-count">0</span>/1500 characters
                            </div>
                        </div>

                        <!-- Proposed Approach -->
                        <div class="mb-4">
                            <label for="proposed_approach" class="form-label">
                                <strong>Your Proposed Partnership Approach *</strong>
                            </label>
                            <textarea class="form-control" id="proposed_approach" name="proposed_approach" 
                                      rows="5" required maxlength="2000"
                                      placeholder="Describe your approach to this partnership. How would you contribute? What's your vision for the collaboration?"><?php echo escape($formData['proposed_approach'] ?? ''); ?></textarea>
                            <div class="char-counter">
                                <span id="approach-count">0</span>/2000 characters
                            </div>
                        </div>

                        <!-- Availability -->
                        <div class="mb-4">
                            <label for="availability" class="form-label">
                                <strong>Availability & Timeline *</strong>
                            </label>
                            <select class="form-select" id="availability" name="availability" required>
                                <option value="">Select your availability</option>
                                <option value="immediate" <?php echo ($formData['availability'] ?? '') === 'immediate' ? 'selected' : ''; ?>>
                                    Available immediately
                                </option>
                                <option value="1-2_weeks" <?php echo ($formData['availability'] ?? '') === '1-2_weeks' ? 'selected' : ''; ?>>
                                    Available in 1-2 weeks
                                </option>
                                <option value="3-4_weeks" <?php echo ($formData['availability'] ?? '') === '3-4_weeks' ? 'selected' : ''; ?>>
                                    Available in 3-4 weeks
                                </option>
                                <option value="1-2_months" <?php echo ($formData['availability'] ?? '') === '1-2_months' ? 'selected' : ''; ?>>
                                    Available in 1-2 months
                                </option>
                                <option value="flexible" <?php echo ($formData['availability'] ?? '') === 'flexible' ? 'selected' : ''; ?>>
                                    Flexible timeline
                                </option>
                            </select>
                        </div>

                        <!-- Additional Notes -->
                        <div class="mb-4">
                            <label for="additional_notes" class="form-label">
                                <strong>Additional Notes</strong>
                            </label>
                            <textarea class="form-control" id="additional_notes" name="additional_notes" 
                                      rows="3" maxlength="1000"
                                      placeholder="Any additional information you'd like to share about this partnership opportunity..."><?php echo escape($formData['additional_notes'] ?? ''); ?></textarea>
                            <div class="char-counter">
                                <span id="notes-count">0</span>/1000 characters
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="partnership.php?slug=<?php echo $opportunity['slug']; ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Partnership
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-handshake me-2"></i>Submit Connection Request
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="partnership-highlight">
                    <h5 class="mb-3"><i class="fas fa-star me-2"></i>Partnership Highlights</h5>
                    <div class="mb-2">
                        <i class="fas fa-chart-line me-2"></i>
                        <span><?php echo escape(PARTNERSHIP_MODELS[$opportunity['partnership_model']]); ?></span>
                    </div>
                    <?php if ($opportunity['revenue_split']): ?>
                        <div class="mb-2">
                            <i class="fas fa-percentage me-2"></i>
                            <span><?php echo escape($opportunity['revenue_split']); ?> Revenue Split</span>
                        </div>
                    <?php endif; ?>
                    <div class="mb-2">
                        <i class="fas fa-users me-2"></i>
                        <span>Collaborative Partnership</span>
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-globe-africa me-2"></i>
                        <span>Southern African Market</span>
                    </div>
                </div>

                <div class="form-card">
                    <h6 class="mb-3">What Happens Next?</h6>
                    <div class="timeline">
                        <div class="d-flex mb-3">
                            <div class="me-3">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 30px; height: 30px;">
                                    <small>1</small>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-1">Review Process</h6>
                                <small class="text-muted">We'll review your connection request within 2-3 business days</small>
                            </div>
                        </div>
                        <div class="d-flex mb-3">
                            <div class="me-3">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 30px; height: 30px;">
                                    <small>2</small>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-1">Initial Discussion</h6>
                                <small class="text-muted">If approved, we'll schedule an initial partnership discussion</small>
                            </div>
                        </div>
                        <div class="d-flex">
                            <div class="me-3">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 30px; height: 30px;">
                                    <small>3</small>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-1">Partnership Launch</h6>
                                <small class="text-muted">Finalize terms and launch your partnership collaboration</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // Character counters
        function setupCharCounter(textareaId, counterId) {
            const textarea = document.getElementById(textareaId);
            const counter = document.getElementById(counterId);
            
            function updateCount() {
                counter.textContent = textarea.value.length;
            }
            
            textarea.addEventListener('input', updateCount);
            updateCount(); // Initial count
        }
        
        // Setup all character counters
        setupCharCounter('partnership_interest', 'interest-count');
        setupCharCounter('relevant_experience', 'experience-count');
        setupCharCounter('portfolio_items', 'portfolio-count');
        setupCharCounter('proposed_approach', 'approach-count');
        setupCharCounter('additional_notes', 'notes-count');
        
        // Form validation
        document.getElementById('connectionForm').addEventListener('submit', function(e) {
            const requiredFields = ['partnership_interest', 'relevant_experience', 'proposed_approach', 'availability'];
            let isValid = true;
            
            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
    </script>
</body>
</html>
