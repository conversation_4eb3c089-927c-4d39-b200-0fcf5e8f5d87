<?php
/**
 * South Safari Partnership Platform - Partnership Collaborator Registration
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once 'includes/init.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    redirect($user['role'] === 'admin' ? ADMIN_URL : COLLABORATOR_URL);
}

$errors = [];
$success = false;
$formData = [];

if (($_SERVER['REQUEST_METHOD'] ?? '') === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Sanitize input
        $formData = [
            'email' => sanitizeInput($_POST['email'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? '',
            'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
            'country' => sanitizeInput($_POST['country'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'company_name' => sanitizeInput($_POST['company_name'] ?? ''),
            'portfolio_url' => sanitizeInput($_POST['portfolio_url'] ?? ''),
            'professional_background' => sanitizeInput($_POST['professional_background'] ?? '')
        ];

        // Validation
        if (empty($formData['email'])) {
            $errors[] = 'Email address is required.';
        } elseif (!isValidEmail($formData['email'])) {
            $errors[] = 'Please enter a valid email address.';
        }

        if (empty($formData['password'])) {
            $errors[] = 'Password is required.';
        } elseif (strlen($formData['password']) < 8) {
            $errors[] = 'Password must be at least 8 characters long.';
        }

        if ($formData['password'] !== $formData['confirm_password']) {
            $errors[] = 'Passwords do not match.';
        }

        if (empty($formData['full_name'])) {
            $errors[] = 'Full name is required.';
        }

        if (!empty($formData['portfolio_url']) && !isValidUrl($formData['portfolio_url'])) {
            $errors[] = 'Please enter a valid portfolio URL.';
        }

        // Check rate limiting
        if (!checkRateLimit('registration', getClientIP(), 3, 3600)) {
            $errors[] = 'Too many registration attempts. Please try again later.';
        }

        // If no errors, attempt registration
        if (empty($errors)) {
            try {
                $result = auth()->register(
                    $formData['email'],
                    $formData['password'],
                    $formData['full_name'],
                    $formData['country'],
                    $formData['phone'],
                    $formData['company_name'],
                    $formData['portfolio_url'],
                    $formData['professional_background']
                );

                if ($result['success']) {
                    $success = true;
                    $requiresVerification = $result['requires_verification'];
                    
                    // Clear form data on success
                    $formData = [];
                }
            } catch (Exception $e) {
                $errors[] = $e->getMessage();
                logSecurityEvent('registration_failed', [
                    'email' => $formData['email'],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}

$pageTitle = 'Join Our Partnership Community';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }
        
        .auth-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        
        .auth-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-body {
            padding: 2rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 176, 116, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #008c5a;
            border-color: #008c5a;
        }
        
        .partnership-benefits {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .benefit-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .benefit-item i {
            color: var(--primary-color);
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1 class="h3 mb-2">
                    <i class="fas fa-handshake me-2"></i>
                    Join South Safari
                </h1>
                <p class="mb-0">Start Your Partnership Journey Today</p>
            </div>
            
            <div class="auth-body">
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i>Registration Successful!</h5>
                        <p class="mb-0">
                            <?php if (isset($requiresVerification) && $requiresVerification): ?>
                                Please check your email and click the verification link to activate your account.
                            <?php else: ?>
                                You can now <a href="login.php">sign in</a> to start exploring partnership opportunities.
                            <?php endif; ?>
                        </p>
                    </div>
                <?php else: ?>
                    
                    <div class="partnership-benefits">
                        <h6 class="fw-bold mb-3">Partnership Benefits:</h6>
                        <div class="benefit-item">
                            <i class="fas fa-handshake"></i>
                            <span>Access to exclusive partnership opportunities</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-chart-line"></i>
                            <span>Recurring revenue sharing models</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-globe-africa"></i>
                            <span>Southern African market access</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-users"></i>
                            <span>Collaborative partnership community</span>
                        </div>
                    </div>

                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo escape($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <?php echo getCSRFInput(); ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo escape($formData['full_name'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo escape($formData['email'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="form-text">Minimum 8 characters</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Country</label>
                                <select class="form-control" id="country" name="country">
                                    <option value="">Select Country</option>
                                    <option value="South Africa" <?php echo ($formData['country'] ?? '') === 'South Africa' ? 'selected' : ''; ?>>South Africa</option>
                                    <option value="Botswana" <?php echo ($formData['country'] ?? '') === 'Botswana' ? 'selected' : ''; ?>>Botswana</option>
                                    <option value="Namibia" <?php echo ($formData['country'] ?? '') === 'Namibia' ? 'selected' : ''; ?>>Namibia</option>
                                    <option value="Zimbabwe" <?php echo ($formData['country'] ?? '') === 'Zimbabwe' ? 'selected' : ''; ?>>Zimbabwe</option>
                                    <option value="Zambia" <?php echo ($formData['country'] ?? '') === 'Zambia' ? 'selected' : ''; ?>>Zambia</option>
                                    <option value="Other" <?php echo ($formData['country'] ?? '') === 'Other' ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo escape($formData['phone'] ?? ''); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="company_name" class="form-label">Company/Organization</label>
                            <input type="text" class="form-control" id="company_name" name="company_name" 
                                   value="<?php echo escape($formData['company_name'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="portfolio_url" class="form-label">Portfolio/Website URL</label>
                            <input type="url" class="form-control" id="portfolio_url" name="portfolio_url" 
                                   value="<?php echo escape($formData['portfolio_url'] ?? ''); ?>"
                                   placeholder="https://your-portfolio.com">
                        </div>

                        <div class="mb-3">
                            <label for="professional_background" class="form-label">Professional Background</label>
                            <textarea class="form-control" id="professional_background" name="professional_background" 
                                      rows="3" placeholder="Brief description of your professional experience and expertise..."><?php echo escape($formData['professional_background'] ?? ''); ?></textarea>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus me-2"></i>Start Partnership Journey
                        </button>
                    </form>
                <?php endif; ?>

                <div class="text-center">
                    <p class="mb-0">Already have an account? <a href="login.php">Sign In</a></p>
                    <p class="mt-2"><a href="index.php" class="text-muted">← Back to Homepage</a></p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
