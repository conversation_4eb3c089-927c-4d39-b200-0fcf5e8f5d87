<?php
/**
 * South Safari Partnership Platform - Partnership Collaborator Dashboard
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once '../includes/init.php';

// Require collaborator authentication
requireAuth();
if (!isCollaborator() && !isAdmin()) {
    redirect('../login.php', 'Access denied. Please sign in with a partnership collaborator account.', 'error');
}

$user = getCurrentUser();
$userId = $user['id'];

// Get user's connections and statistics
$connections = getUserConnections($userId);
$stats = [
    'total_connections' => count($connections),
    'pending_connections' => count(array_filter($connections, fn($c) => in_array($c['status'], ['submitted', 'reviewing']))),
    'approved_connections' => count(array_filter($connections, fn($c) => $c['status'] === 'approved')),
    'active_partnerships' => db()->count('active_partnerships', 'collaborator_id = ? AND status = ?', [$userId, 'active'])
];

// Get recent partnership opportunities
$recentOpportunities = getPartnershipOpportunities(6, null, 'active');

// Get recent messages (if any)
$recentMessages = db()->fetchAll(
    "SELECT pm.*, u.full_name as sender_name 
     FROM partnership_messages pm 
     JOIN users u ON pm.sender_id = u.id 
     WHERE pm.recipient_id = ? 
     ORDER BY pm.created_at DESC 
     LIMIT 5",
    [$userId]
);

$pageTitle = 'Partnership Dashboard';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            height: 100%;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .opportunity-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            transition: transform 0.2s;
        }
        
        .opportunity-card:hover {
            transform: translateY(-2px);
        }
        
        .feature-badge {
            background: #E8F5F0;
            color: var(--primary-color);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }
        
        .connection-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-submitted { background: #cce5ff; color: #0066cc; }
        .status-reviewing { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-handshake me-2"></i>South Safari
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="connections.php">My Connections</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../partnerships.php">Browse Opportunities</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i><?php echo escape($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">Welcome back, <?php echo escape($user['full_name']); ?>!</h1>
                    <p class="mb-0">Manage your partnership connections and discover new opportunities</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="../partnerships.php" class="btn btn-light">
                        <i class="fas fa-search me-2"></i>Explore Partnerships
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-4">
        <?php displayFlashMessage(); ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_connections']; ?></div>
                    <div class="stat-label">Total Connections</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['pending_connections']; ?></div>
                    <div class="stat-label">Pending Review</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['approved_connections']; ?></div>
                    <div class="stat-label">Approved Connections</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['active_partnerships']; ?></div>
                    <div class="stat-label">Active Partnerships</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Connections -->
            <div class="col-lg-8">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4>Recent Connections</h4>
                    <a href="connections.php" class="btn btn-outline-primary btn-sm">View All</a>
                </div>

                <?php if (empty($connections)): ?>
                    <div class="opportunity-card text-center">
                        <i class="fas fa-handshake text-muted fs-1 mb-3"></i>
                        <h5>No Connections Yet</h5>
                        <p class="text-muted">Start connecting with partnership opportunities to build your portfolio.</p>
                        <a href="../partnerships.php" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Browse Opportunities
                        </a>
                    </div>
                <?php else: ?>
                    <?php foreach (array_slice($connections, 0, 5) as $connection): ?>
                        <div class="opportunity-card">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0"><?php echo escape($connection['title']); ?></h6>
                                <span class="connection-status status-<?php echo $connection['status']; ?>">
                                    <?php echo CONNECTION_STATUSES[$connection['status']] ?? ucfirst($connection['status']); ?>
                                </span>
                            </div>
                            <p class="text-muted small mb-2">
                                <i class="fas fa-tag me-1"></i><?php echo escape($connection['category']); ?>
                                <span class="ms-3">
                                    <i class="fas fa-calendar me-1"></i><?php echo formatDate($connection['created_at']); ?>
                                </span>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    Connected <?php echo timeAgo($connection['created_at']); ?>
                                </small>
                                <a href="connection-details.php?id=<?php echo $connection['id']; ?>" class="btn btn-sm btn-outline-primary">
                                    View Details
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Recent Opportunities -->
                <div class="mb-4">
                    <h5 class="mb-3">New Opportunities</h5>
                    <?php foreach (array_slice($recentOpportunities, 0, 3) as $opportunity): ?>
                        <div class="opportunity-card">
                            <h6 class="mb-2"><?php echo escape($opportunity['title']); ?></h6>
                            <p class="small text-muted mb-2"><?php echo truncate($opportunity['short_description'], 80); ?></p>
                            <div class="mb-2">
                                <?php 
                                $features = json_decode($opportunity['product_features'], true);
                                if ($features && is_array($features)) {
                                    foreach (array_slice($features, 0, 2) as $feature) {
                                        echo "<span class='feature-badge'>" . escape($feature) . "</span>";
                                    }
                                }
                                ?>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted"><?php echo escape($opportunity['category']); ?></small>
                                <a href="../partnership.php?slug=<?php echo $opportunity['slug']; ?>" class="btn btn-sm btn-primary">
                                    Connect
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Quick Actions -->
                <div class="opportunity-card">
                    <h6 class="mb-3">Quick Actions</h6>
                    <div class="d-grid gap-2">
                        <a href="../partnerships.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-search me-2"></i>Browse All Opportunities
                        </a>
                        <a href="profile.php" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-user me-2"></i>Update Profile
                        </a>
                        <a href="connections.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-handshake me-2"></i>Manage Connections
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
