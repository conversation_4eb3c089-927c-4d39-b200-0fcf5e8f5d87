<?php
/**
 * South Safari Partnership Platform - Collaborator Connections Management
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once '../includes/init.php';

// Require collaborator authentication
requireAuth();
if (!isCollaborator() && !isAdmin()) {
    redirect('../login.php', 'Access denied. Please sign in with a partnership collaborator account.', 'error');
}

$user = getCurrentUser();
$userId = $user['id'];

// Pagination and filtering
$page = max(1, (int)($_GET['page'] ?? 1));
$perPage = CONNECTIONS_PER_PAGE;
$offset = ($page - 1) * $perPage;

$statusFilter = sanitizeInput($_GET['status'] ?? '');

// Build query
$whereClause = "pc.collaborator_id = ?";
$params = [$userId];

if ($statusFilter) {
    $whereClause .= " AND pc.status = ?";
    $params[] = $statusFilter;
}

// Get total count for pagination
$totalConnections = db()->count('partnership_connections pc', $whereClause, $params);
$totalPages = ceil($totalConnections / $perPage);

// Get connections with opportunity details
$connections = db()->fetchAll(
    "SELECT pc.*, po.title, po.slug, po.category, po.partnership_model, po.revenue_split 
     FROM partnership_connections pc 
     JOIN partnership_opportunities po ON pc.opportunity_id = po.id 
     WHERE {$whereClause} 
     ORDER BY pc.created_at DESC 
     LIMIT {$perPage} OFFSET {$offset}",
    $params
);

// Get connection statistics
$stats = [
    'total' => db()->count('partnership_connections', 'collaborator_id = ?', [$userId]),
    'submitted' => db()->count('partnership_connections', 'collaborator_id = ? AND status = ?', [$userId, 'submitted']),
    'reviewing' => db()->count('partnership_connections', 'collaborator_id = ? AND status = ?', [$userId, 'reviewing']),
    'approved' => db()->count('partnership_connections', 'collaborator_id = ? AND status = ?', [$userId, 'approved']),
    'rejected' => db()->count('partnership_connections', 'collaborator_id = ? AND status = ?', [$userId, 'rejected'])
];

$pageTitle = 'My Partnership Connections';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .connections-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            height: 100%;
            transition: transform 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .connection-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            transition: transform 0.2s;
        }
        
        .connection-card:hover {
            transform: translateY(-2px);
        }
        
        .connection-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-submitted { background: #cce5ff; color: #0066cc; }
        .status-reviewing { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-withdrawn { background: #e2e3e5; color: #383d41; }
        
        .filter-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }
        
        .partnership-model-badge {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-handshake me-2"></i>South Safari
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="connections.php">My Connections</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../partnerships.php">Browse Opportunities</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i><?php echo escape($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <div class="connections-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">My Partnership Connections</h1>
                    <p class="mb-0">Track and manage your partnership connection requests</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="../partnerships.php" class="btn btn-light">
                        <i class="fas fa-search me-2"></i>Browse More Partnerships
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-4">
        <?php displayFlashMessage(); ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total']; ?></div>
                    <div class="stat-label">Total Connections</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['submitted'] + $stats['reviewing']; ?></div>
                    <div class="stat-label">Under Review</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['approved']; ?></div>
                    <div class="stat-label">Approved</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['rejected']; ?></div>
                    <div class="stat-label">Not Approved</div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="status" class="form-label">Filter by Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All Statuses</option>
                        <?php foreach (CONNECTION_STATUSES as $key => $label): ?>
                            <option value="<?php echo $key; ?>" <?php echo $statusFilter === $key ? 'selected' : ''; ?>>
                                <?php echo escape($label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i>Filter
                    </button>
                </div>
                <div class="col-md-6 text-md-end">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <?php if ($statusFilter): ?>
                            <a href="connections.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Clear Filter
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>

        <!-- Connections List -->
        <?php if (empty($connections)): ?>
            <div class="empty-state">
                <i class="fas fa-handshake"></i>
                <h4>
                    <?php if ($statusFilter): ?>
                        No connections found with status "<?php echo CONNECTION_STATUSES[$statusFilter]; ?>"
                    <?php else: ?>
                        No Partnership Connections Yet
                    <?php endif; ?>
                </h4>
                <p class="text-muted mb-4">
                    <?php if ($statusFilter): ?>
                        Try adjusting your filter or browse all connections.
                    <?php else: ?>
                        Start connecting with partnership opportunities to build your collaboration portfolio.
                    <?php endif; ?>
                </p>
                <?php if ($statusFilter): ?>
                    <a href="connections.php" class="btn btn-primary">
                        <i class="fas fa-eye me-2"></i>View All Connections
                    </a>
                <?php else: ?>
                    <a href="../partnerships.php" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Browse Partnership Opportunities
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <?php foreach ($connections as $connection): ?>
                <div class="connection-card">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h5 class="mb-2">
                                <a href="../partnership.php?slug=<?php echo $connection['slug']; ?>" class="text-decoration-none">
                                    <?php echo escape($connection['title']); ?>
                                </a>
                            </h5>
                            <div class="mb-2">
                                <span class="badge bg-secondary me-2"><?php echo escape($connection['category']); ?></span>
                                <span class="partnership-model-badge">
                                    <?php echo escape($connection['revenue_split'] ?: PARTNERSHIP_MODELS[$connection['partnership_model']]); ?>
                                </span>
                            </div>
                        </div>
                        <div class="text-end">
                            <span class="connection-status status-<?php echo $connection['status']; ?>">
                                <?php echo CONNECTION_STATUSES[$connection['status']] ?? ucfirst($connection['status']); ?>
                            </span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-2">
                                <strong>Partnership Interest:</strong>
                                <p class="text-muted mb-1"><?php echo truncate($connection['partnership_interest'], 150); ?></p>
                            </div>
                            <div class="mb-2">
                                <strong>Proposed Approach:</strong>
                                <p class="text-muted mb-1"><?php echo truncate($connection['proposed_approach'], 150); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Submitted <?php echo formatDate($connection['submitted_at']); ?>
                                </small>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Availability: <?php echo escape(str_replace('_', ' ', ucfirst($connection['availability']))); ?>
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <small class="text-muted">
                            Connection ID: #<?php echo str_pad($connection['id'], 6, '0', STR_PAD_LEFT); ?>
                        </small>
                        <div>
                            <a href="connection-details.php?id=<?php echo $connection['id']; ?>" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            <a href="../partnership.php?slug=<?php echo $connection['slug']; ?>" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>View Partnership
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?php echo generatePagination($page, $totalPages, 'connections.php', [
                        'status' => $statusFilter
                    ]); ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
