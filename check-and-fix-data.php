<?php
/**
 * Check and fix sample data
 */

define('SS_DIRECT_ACCESS', true);
require_once 'includes/config.php';

try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);

    echo "Checking database status...\n";
    echo "Database: " . DB_NAME . "\n";
    
    // Check users first
    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "Total users: {$userCount}\n";

    $adminCount = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'")->fetchColumn();
    echo "Admin users: {$adminCount}\n";

    // Get admin user ID
    $adminUser = $pdo->query("SELECT id FROM users WHERE role = 'admin' LIMIT 1")->fetch();
    $adminId = $adminUser ? $adminUser['id'] : null;
    echo "Admin user ID: " . ($adminId ?: 'None') . "\n";

    // Check partnership opportunities
    $count = $pdo->query("SELECT COUNT(*) FROM partnership_opportunities")->fetchColumn();
    echo "Total partnership opportunities: {$count}\n";

    $activeCount = $pdo->query("SELECT COUNT(*) FROM partnership_opportunities WHERE status = 'active'")->fetchColumn();
    echo "Active partnership opportunities: {$activeCount}\n";
    
    // If no opportunities and we have an admin user, insert them manually
    if ($count == 0 && $adminId) {
        echo "Inserting sample partnership opportunities...\n";
        
        $opportunities = [
            [
                'title' => 'Township Food Delivery Platform',
                'slug' => 'township-food-delivery',
                'category' => 'Food Delivery',
                'short_description' => 'Connect local restaurants with customers in underserved areas. Similar to Uber Eats but for townships.',
                'full_description' => 'This partnership opportunity focuses on building a comprehensive food delivery platform specifically designed for township communities in Southern Africa.',
                'product_features' => '["Real-time Ordering", "GPS Tracking", "Payment Integration", "Multi-language Support"]',
                'partnership_model' => 'revenue_share',
                'revenue_split' => '50/50',
                'market_opportunity' => 'Township markets represent a significant untapped opportunity with over 10 million potential customers across South Africa alone.',
                'expected_timeline' => '6-8 months for MVP, 12 months for full platform',
                'partnership_terms' => 'Equal partnership with shared decision-making and transparent revenue distribution.',
                'requirements' => 'Experience in mobile app development, understanding of local market dynamics.',
                'status' => 'active',
                'featured' => 1,
                'display_order' => 1,
                'created_by' => $adminId
            ],
            [
                'title' => 'Salon & Spa Booking System',
                'slug' => 'salon-spa-booking',
                'category' => 'Service Booking',
                'short_description' => 'Digital booking platform for beauty services with SMS notifications and payment integration.',
                'full_description' => 'Revolutionary booking platform designed to modernize the beauty industry in Southern Africa.',
                'product_features' => '["Online Booking", "SMS Reminders", "Payment Processing", "Staff Management"]',
                'partnership_model' => 'equity_partnership',
                'revenue_split' => 'Equity Partnership',
                'market_opportunity' => 'The beauty industry in Southern Africa is valued at over $2 billion with significant digitization opportunities.',
                'expected_timeline' => '4-6 months for core features, 8 months for full deployment',
                'partnership_terms' => 'Equity-based partnership with shared ownership and long-term growth potential.',
                'requirements' => 'Experience in service industry platforms, understanding of appointment scheduling systems.',
                'status' => 'active',
                'featured' => 1,
                'display_order' => 2,
                'created_by' => $adminId
            ],
            [
                'title' => 'Stokvel Management Platform',
                'slug' => 'stokvel-management',
                'category' => 'FinTech',
                'short_description' => 'Digital solution for traditional savings groups with automated contributions and payouts.',
                'full_description' => 'Transform traditional stokvel operations through digital innovation.',
                'product_features' => '["Automated Contributions", "Member Management", "Payout Scheduling", "Financial Reports"]',
                'partnership_model' => 'profit_sharing',
                'revenue_split' => 'Profit Sharing',
                'market_opportunity' => 'Stokvels handle over R50 billion annually in South Africa.',
                'expected_timeline' => '8-10 months for regulatory compliance and core features',
                'partnership_terms' => 'Profit-sharing model with performance-based incentives.',
                'requirements' => 'FinTech experience, understanding of South African financial regulations.',
                'status' => 'active',
                'featured' => 1,
                'display_order' => 3,
                'created_by' => $adminId
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO partnership_opportunities 
            (title, slug, category, short_description, full_description, product_features, 
             partnership_model, revenue_split, market_opportunity, expected_timeline, 
             partnership_terms, requirements, status, featured, display_order, created_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($opportunities as $opp) {
            $stmt->execute([
                $opp['title'], $opp['slug'], $opp['category'], $opp['short_description'],
                $opp['full_description'], $opp['product_features'], $opp['partnership_model'],
                $opp['revenue_split'], $opp['market_opportunity'], $opp['expected_timeline'],
                $opp['partnership_terms'], $opp['requirements'], $opp['status'],
                $opp['featured'], $opp['display_order'], $opp['created_by']
            ]);
        }
        
        echo "Sample opportunities inserted successfully!\n";
    }
    
    // Final check
    $finalCount = $pdo->query("SELECT COUNT(*) FROM partnership_opportunities WHERE status = 'active'")->fetchColumn();
    echo "Final active partnership opportunities: {$finalCount}\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
