<?php
/**
 * South Safari Partnership Platform - Admin Dashboard
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once '../includes/init.php';

// Require admin authentication
requireAdmin();

$user = getCurrentUser();

// Get dashboard statistics
$stats = [
    'total_opportunities' => db()->count('partnership_opportunities'),
    'active_opportunities' => db()->count('partnership_opportunities', 'status = ?', ['active']),
    'total_collaborators' => db()->count('users', 'role = ?', ['collaborator']),
    'active_collaborators' => db()->count('users', 'role = ? AND status = ?', ['collaborator', 'active']),
    'total_connections' => db()->count('partnership_connections'),
    'pending_connections' => db()->count('partnership_connections', 'status IN (?, ?)', ['submitted', 'reviewing']),
    'approved_connections' => db()->count('partnership_connections', 'status = ?', ['approved']),
    'active_partnerships' => db()->count('active_partnerships', 'status = ?', ['active'])
];

// Get recent connections
$recentConnections = db()->fetchAll(
    "SELECT pc.*, po.title as opportunity_title, u.full_name as collaborator_name 
     FROM partnership_connections pc 
     JOIN partnership_opportunities po ON pc.opportunity_id = po.id 
     JOIN users u ON pc.collaborator_id = u.id 
     ORDER BY pc.created_at DESC 
     LIMIT 10"
);

// Get recent collaborator registrations
$recentCollaborators = db()->fetchAll(
    "SELECT * FROM users 
     WHERE role = 'collaborator' 
     ORDER BY created_at DESC 
     LIMIT 5"
);

$pageTitle = 'Admin Dashboard';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #333 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            height: 100%;
            transition: transform 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .admin-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .connection-item {
            border-bottom: 1px solid #eee;
            padding: 1rem 0;
        }
        
        .connection-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-submitted { background: #cce5ff; color: #0066cc; }
        .status-reviewing { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        
        .quick-action-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-decoration: none;
            display: block;
            transition: transform 0.2s;
        }
        
        .quick-action-card:hover {
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-handshake me-2"></i>South Safari Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="opportunities.php">Partnership Opportunities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="collaborators.php">Collaborators</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="connections.php">Connections</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-1"></i><?php echo escape($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">Admin Dashboard</h1>
                    <p class="mb-0">Manage partnership opportunities, collaborators, and platform settings</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="opportunities.php?action=create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>New Opportunity
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-4">
        <?php displayFlashMessage(); ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['active_opportunities']; ?></div>
                    <div class="stat-label">Active Opportunities</div>
                    <small class="text-muted"><?php echo $stats['total_opportunities']; ?> total</small>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['active_collaborators']; ?></div>
                    <div class="stat-label">Active Collaborators</div>
                    <small class="text-muted"><?php echo $stats['total_collaborators']; ?> total</small>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['pending_connections']; ?></div>
                    <div class="stat-label">Pending Connections</div>
                    <small class="text-muted"><?php echo $stats['total_connections']; ?> total</small>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['active_partnerships']; ?></div>
                    <div class="stat-label">Active Partnerships</div>
                    <small class="text-muted"><?php echo $stats['approved_connections']; ?> approved</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Connections -->
            <div class="col-lg-8">
                <div class="admin-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">Recent Partnership Connections</h5>
                        <a href="connections.php" class="btn btn-outline-primary btn-sm">View All</a>
                    </div>

                    <?php if (empty($recentConnections)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-handshake text-muted fs-1 mb-3"></i>
                            <p class="text-muted">No partnership connections yet.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recentConnections as $connection): ?>
                            <div class="connection-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1"><?php echo escape($connection['collaborator_name']); ?></h6>
                                        <p class="mb-1 text-muted"><?php echo escape($connection['opportunity_title']); ?></p>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i><?php echo formatDateTime($connection['created_at']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="status-badge status-<?php echo $connection['status']; ?>">
                                            <?php echo CONNECTION_STATUSES[$connection['status']] ?? ucfirst($connection['status']); ?>
                                        </span>
                                        <div class="mt-2">
                                            <a href="connection-details.php?id=<?php echo $connection['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                Review
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Quick Actions -->
                <div class="mb-4">
                    <h5 class="mb-3">Quick Actions</h5>
                    <div class="row g-3">
                        <div class="col-12">
                            <a href="opportunities.php?action=create" class="quick-action-card">
                                <i class="fas fa-plus fs-3 mb-2"></i>
                                <h6 class="mb-1">Create Opportunity</h6>
                                <small>Add new partnership opportunity</small>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="collaborators.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-users d-block mb-1"></i>
                                <small>Collaborators</small>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="connections.php" class="btn btn-outline-info w-100">
                                <i class="fas fa-handshake d-block mb-1"></i>
                                <small>Connections</small>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Collaborators -->
                <div class="admin-card">
                    <h6 class="mb-3">Recent Collaborators</h6>
                    <?php if (empty($recentCollaborators)): ?>
                        <p class="text-muted small">No new collaborators yet.</p>
                    <?php else: ?>
                        <?php foreach ($recentCollaborators as $collaborator): ?>
                            <div class="d-flex align-items-center mb-2">
                                <img src="<?php echo getUserAvatarUrl($collaborator); ?>" 
                                     alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                                <div class="flex-grow-1">
                                    <div class="fw-bold small"><?php echo escape($collaborator['full_name']); ?></div>
                                    <div class="text-muted small"><?php echo timeAgo($collaborator['created_at']); ?></div>
                                </div>
                                <a href="collaborator-details.php?id=<?php echo $collaborator['id']; ?>" 
                                   class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
