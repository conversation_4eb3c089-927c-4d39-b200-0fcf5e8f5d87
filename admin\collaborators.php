<?php
/**
 * South Safari Partnership Platform - Admin Collaborators Management
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once '../includes/init.php';

// Require admin authentication
requireAdmin();

$user = getCurrentUser();

// Pagination and filtering
$page = max(1, (int)($_GET['page'] ?? 1));
$perPage = 20;
$offset = ($page - 1) * $perPage;

$search = sanitizeInput($_GET['search'] ?? '');
$statusFilter = sanitizeInput($_GET['status'] ?? '');
$countryFilter = sanitizeInput($_GET['country'] ?? '');

// Build query
$whereClause = "role = 'collaborator'";
$params = [];

if ($search) {
    $whereClause .= " AND (full_name LIKE ? OR email LIKE ? OR company_name LIKE ?)";
    $searchTerm = '%' . db()->escapeLike($search) . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if ($statusFilter) {
    $whereClause .= " AND status = ?";
    $params[] = $statusFilter;
}

if ($countryFilter) {
    $whereClause .= " AND country = ?";
    $params[] = $countryFilter;
}

// Get total count for pagination
$totalCollaborators = db()->count('users', $whereClause, $params);
$totalPages = ceil($totalCollaborators / $perPage);

// Get collaborators with connection statistics
$collaborators = db()->fetchAll(
    "SELECT u.*, 
            COUNT(pc.id) as total_connections,
            COUNT(CASE WHEN pc.status = 'approved' THEN 1 END) as approved_connections,
            COUNT(CASE WHEN ap.id IS NOT NULL THEN 1 END) as active_partnerships
     FROM users u 
     LEFT JOIN partnership_connections pc ON u.id = pc.collaborator_id 
     LEFT JOIN active_partnerships ap ON u.id = ap.collaborator_id AND ap.status = 'active'
     WHERE {$whereClause} 
     GROUP BY u.id
     ORDER BY u.created_at DESC 
     LIMIT {$perPage} OFFSET {$offset}",
    $params
);

// Get countries for filter
$countries = db()->fetchAll(
    "SELECT DISTINCT country FROM users WHERE role = 'collaborator' AND country IS NOT NULL ORDER BY country"
);

$pageTitle = 'Partnership Collaborators';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .admin-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .collaborator-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            transition: transform 0.2s;
        }
        
        .collaborator-card:hover {
            transform: translateY(-2px);
        }
        
        .collaborator-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-right: 1rem;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
        
        .stat-item {
            text-align: center;
            padding: 0.5rem;
        }
        
        .stat-number {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-handshake me-2"></i>South Safari Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="opportunities.php">Partnership Opportunities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="collaborators.php">Collaborators</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="connections.php">Connections</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-1"></i><?php echo escape($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <?php displayFlashMessage(); ?>

        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Partnership Collaborators</h2>
            <div>
                <span class="badge bg-primary me-2"><?php echo $totalCollaborators; ?> Total</span>
            </div>
        </div>

        <!-- Filters -->
        <div class="admin-card">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Collaborators</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="Search by name, email, or company..." value="<?php echo escape($search); ?>">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="inactive" <?php echo $statusFilter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="country" class="form-label">Country</label>
                    <select class="form-control" id="country" name="country">
                        <option value="">All Countries</option>
                        <?php foreach ($countries as $country): ?>
                            <option value="<?php echo escape($country['country']); ?>" 
                                    <?php echo $countryFilter === $country['country'] ? 'selected' : ''; ?>>
                                <?php echo escape($country['country']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </form>
            
            <?php if ($search || $statusFilter || $countryFilter): ?>
                <div class="mt-3">
                    <a href="collaborators.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i>Clear Filters
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Collaborators List -->
        <?php if (empty($collaborators)): ?>
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h4>No Partnership Collaborators Found</h4>
                <p class="text-muted mb-4">
                    <?php if ($search || $statusFilter || $countryFilter): ?>
                        Try adjusting your search criteria to see more collaborators.
                    <?php else: ?>
                        Partnership collaborators will appear here as they register on the platform.
                    <?php endif; ?>
                </p>
                <?php if ($search || $statusFilter || $countryFilter): ?>
                    <a href="collaborators.php" class="btn btn-primary">
                        <i class="fas fa-eye me-2"></i>View All Collaborators
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <?php foreach ($collaborators as $collaborator): ?>
                <div class="collaborator-card">
                    <div class="d-flex align-items-start">
                        <div class="collaborator-avatar">
                            <?php echo strtoupper(substr($collaborator['full_name'], 0, 1)); ?>
                        </div>
                        
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h5 class="mb-1"><?php echo escape($collaborator['full_name']); ?></h5>
                                    <p class="text-muted mb-1"><?php echo escape($collaborator['email']); ?></p>
                                    <?php if ($collaborator['company_name']): ?>
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-building me-1"></i><?php echo escape($collaborator['company_name']); ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                                <div class="text-end">
                                    <span class="status-badge status-<?php echo $collaborator['status']; ?>">
                                        <?php echo ucfirst($collaborator['status']); ?>
                                    </span>
                                    <?php if ($collaborator['email_verified']): ?>
                                        <span class="badge bg-info ms-1">Verified</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <?php if ($collaborator['professional_background']): ?>
                                        <p class="text-muted small mb-2">
                                            <strong>Background:</strong> <?php echo truncate($collaborator['professional_background'], 150); ?>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <div class="d-flex flex-wrap gap-2 mb-2">
                                        <?php if ($collaborator['country']): ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-globe me-1"></i><?php echo escape($collaborator['country']); ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($collaborator['portfolio_url']): ?>
                                            <a href="<?php echo escape($collaborator['portfolio_url']); ?>" target="_blank" class="badge bg-info text-decoration-none">
                                                <i class="fas fa-external-link-alt me-1"></i>Portfolio
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="row g-2">
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <div class="stat-number"><?php echo $collaborator['total_connections']; ?></div>
                                                <div class="stat-label">Connections</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <div class="stat-number"><?php echo $collaborator['approved_connections']; ?></div>
                                                <div class="stat-label">Approved</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <div class="stat-number"><?php echo $collaborator['active_partnerships']; ?></div>
                                                <div class="stat-label">Active</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Joined <?php echo formatDate($collaborator['created_at']); ?>
                                    <?php if ($collaborator['last_login']): ?>
                                        • Last active <?php echo timeAgo($collaborator['last_login']); ?>
                                    <?php endif; ?>
                                </small>
                                <div>
                                    <a href="collaborator-details.php?id=<?php echo $collaborator['id']; ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    <?php if ($collaborator['total_connections'] > 0): ?>
                                        <a href="connections.php?collaborator=<?php echo $collaborator['id']; ?>" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-handshake me-1"></i>View Connections
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="d-flex justify-content-center">
                    <?php echo generatePagination($page, $totalPages, 'collaborators.php', [
                        'search' => $search,
                        'status' => $statusFilter,
                        'country' => $countryFilter
                    ]); ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
