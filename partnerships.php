<?php
/**
 * South Safari Partnership Platform - Browse Partnership Opportunities
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once 'includes/init.php';

// Pagination and filtering
$page = max(1, (int)($_GET['page'] ?? 1));
$perPage = PARTNERSHIPS_PER_PAGE;
$offset = ($page - 1) * $perPage;

$search = sanitizeInput($_GET['search'] ?? '');
$categoryFilter = sanitizeInput($_GET['category'] ?? '');
$modelFilter = sanitizeInput($_GET['model'] ?? '');

// Build query
$whereClause = "status = 'active'";
$params = [];

if ($search) {
    $whereClause .= " AND (title LIKE ? OR short_description LIKE ?)";
    $searchTerm = '%' . db()->escapeLike($search) . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if ($categoryFilter) {
    $whereClause .= " AND category = ?";
    $params[] = $categoryFilter;
}

if ($modelFilter) {
    $whereClause .= " AND partnership_model = ?";
    $params[] = $modelFilter;
}

// Get total count for pagination
$totalOpportunities = db()->count('partnership_opportunities', $whereClause, $params);
$totalPages = ceil($totalOpportunities / $perPage);

// Get opportunities
$opportunities = db()->fetchAll(
    "SELECT * FROM partnership_opportunities 
     WHERE {$whereClause} 
     ORDER BY featured DESC, display_order ASC, created_at DESC 
     LIMIT {$perPage} OFFSET {$offset}",
    $params
);

// Get categories for filter
$categories = db()->fetchAll(
    "SELECT DISTINCT category FROM partnership_opportunities WHERE status = 'active' ORDER BY category"
);

$pageTitle = 'Browse Partnership Opportunities';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .partnerships-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            color: white;
            padding: 3rem 0;
        }
        
        .filter-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }
        
        .partnership-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            transition: all 0.3s;
            height: 100%;
            border: 2px solid transparent;
            display: flex;
            flex-direction: column;
            min-height: 400px;
        }
        
        .partnership-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }
        
        .partnership-category {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .partnership-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--secondary-color);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.4;
            min-height: 3.5rem;
        }
        
        .partnership-description {
            color: #666;
            margin-bottom: 1rem;
            font-size: 0.95rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.5;
            flex-grow: 1;
        }
        
        .feature-list {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .feature-badge {
            background: #E8F5F0;
            color: var(--primary-color);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            border: 1px solid rgba(0, 176, 116, 0.2);
        }
        
        .card-bottom {
            margin-top: auto;
            padding-top: 1rem;
        }
        
        .partnership-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .revenue-info {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .btn-connect {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
        }
        
        .btn-connect:hover {
            background-color: #008c5a;
            color: white;
            transform: translateY(-1px);
        }
        
        .featured-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: #ffc107;
            color: #000;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 176, 116, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #008c5a;
            border-color: #008c5a;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-handshake me-2"></i>South Safari
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="partnerships.php">Partnerships</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#how-it-works">How It Works</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <?php $currentUser = getCurrentUser(); ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i><?php echo escape($currentUser['full_name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="<?php echo $currentUser['role'] === 'admin' ? 'admin/' : 'collaborator/'; ?>">
                                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">Sign In</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">Join Partnership</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="partnerships-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">Partnership Opportunities</h1>
                    <p class="lead mb-0">Discover meaningful partnerships that transform your expertise into sustainable revenue streams</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="text-white">
                        <div class="h4 mb-1"><?php echo $totalOpportunities; ?></div>
                        <div>Active Partnerships</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container my-5">
        <?php displayFlashMessage(); ?>

        <!-- Filters -->
        <div class="filter-card">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Partnerships</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="Search by title or description..." value="<?php echo escape($search); ?>">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-control" id="category" name="category">
                        <option value="">All Categories</option>
                        <?php foreach (PARTNERSHIP_CATEGORIES as $key => $label): ?>
                            <option value="<?php echo $key; ?>" <?php echo $categoryFilter === $key ? 'selected' : ''; ?>>
                                <?php echo escape($label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="model" class="form-label">Partnership Model</label>
                    <select class="form-control" id="model" name="model">
                        <option value="">All Models</option>
                        <?php foreach (PARTNERSHIP_MODELS as $key => $label): ?>
                            <option value="<?php echo $key; ?>" <?php echo $modelFilter === $key ? 'selected' : ''; ?>>
                                <?php echo escape($label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Results -->
        <?php if (empty($opportunities)): ?>
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <h4>No Partnership Opportunities Found</h4>
                <p class="text-muted mb-4">
                    <?php if ($search || $categoryFilter || $modelFilter): ?>
                        Try adjusting your search criteria or browse all partnerships.
                    <?php else: ?>
                        New partnership opportunities are being added regularly. Check back soon!
                    <?php endif; ?>
                </p>
                <?php if ($search || $categoryFilter || $modelFilter): ?>
                    <a href="partnerships.php" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>View All Partnerships
                    </a>
                <?php else: ?>
                    <a href="register.php" class="btn btn-primary">
                        <i class="fas fa-handshake me-2"></i>Join Partnership Community
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="row g-4">
                <?php foreach ($opportunities as $opportunity): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="partnership-card position-relative">
                            <?php if ($opportunity['featured']): ?>
                                <span class="featured-badge">Featured</span>
                            <?php endif; ?>
                            
                            <span class="partnership-category"><?php echo escape($opportunity['category']); ?></span>
                            <h4 class="partnership-title"><?php echo escape($opportunity['title']); ?></h4>
                            <p class="partnership-description"><?php echo escape($opportunity['short_description']); ?></p>
                            
                            <div class="feature-list">
                                <?php 
                                $features = json_decode($opportunity['product_features'], true);
                                if ($features && is_array($features)) {
                                    foreach (array_slice($features, 0, 4) as $feature) {
                                        echo "<span class='feature-badge'>" . escape($feature) . "</span>";
                                    }
                                    if (count($features) > 4) {
                                        echo "<span class='feature-badge'>+" . (count($features) - 4) . " more</span>";
                                    }
                                }
                                ?>
                            </div>
                            
                            <div class="card-bottom">
                                <div class="partnership-meta">
                                    <span class="revenue-info">
                                        <?php echo escape($opportunity['revenue_split'] ?: PARTNERSHIP_MODELS[$opportunity['partnership_model']]); ?>
                                    </span>
                                    <small class="text-muted">
                                        <?php echo formatDate($opportunity['created_at']); ?>
                                    </small>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="partnership.php?slug=<?php echo $opportunity['slug']; ?>" class="btn btn-outline-primary btn-sm">
                                        View Details
                                    </a>
                                    <a href="partnership.php?slug=<?php echo $opportunity['slug']; ?>" class="btn-connect">
                                        Connect
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="d-flex justify-content-center mt-5">
                    <?php echo generatePagination($page, $totalPages, 'partnerships.php', [
                        'search' => $search,
                        'category' => $categoryFilter,
                        'model' => $modelFilter
                    ]); ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
