<?php
/**
 * South Safari Partnership Platform - Connection Details
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

require_once '../includes/init.php';

// Require collaborator authentication
requireAuth();
if (!isCollaborator() && !isAdmin()) {
    redirect('../login.php', 'Access denied. Please sign in with a partnership collaborator account.', 'error');
}

$user = getCurrentUser();
$connectionId = (int)($_GET['id'] ?? 0);

// Get connection details with opportunity information
$connection = db()->fetchRow(
    "SELECT pc.*, po.title, po.slug, po.category, po.partnership_model, po.revenue_split, po.short_description,
            u.full_name as admin_name
     FROM partnership_connections pc 
     JOIN partnership_opportunities po ON pc.opportunity_id = po.id 
     LEFT JOIN users u ON pc.reviewed_by = u.id
     WHERE pc.id = ? AND pc.collaborator_id = ?",
    [$connectionId, $user['id']]
);

if (!$connection) {
    redirect('connections.php', 'Connection not found.', 'error');
}

// Handle withdrawal request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'withdraw') {
    if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
        if (in_array($connection['status'], ['submitted', 'reviewing'])) {
            try {
                db()->update('partnership_connections', 
                    ['status' => 'withdrawn'], 
                    'id = ?', 
                    [$connectionId]
                );
                
                db()->logActivity($user['id'], 'connection_withdrawn', 'partnership_connection', $connectionId);
                
                redirect('connections.php', 'Connection request has been withdrawn successfully.', 'success');
            } catch (Exception $e) {
                $error = 'Failed to withdraw connection. Please try again.';
            }
        } else {
            $error = 'This connection cannot be withdrawn at this time.';
        }
    } else {
        $error = 'Invalid security token. Please try again.';
    }
}

$pageTitle = "Connection Details - {$connection['title']}";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
        }
        
        .connection-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }
        
        .connection-status {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
        }
        
        .status-submitted { background: #cce5ff; color: #0066cc; }
        .status-reviewing { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-withdrawn { background: #e2e3e5; color: #383d41; }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--secondary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }
        
        .info-item {
            margin-bottom: 1rem;
        }
        
        .info-label {
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 0.25rem;
        }
        
        .info-value {
            color: #666;
            line-height: 1.6;
        }
        
        .timeline-item {
            display: flex;
            align-items-flex-start;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .timeline-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .timeline-content h6 {
            margin-bottom: 0.25rem;
            color: var(--secondary-color);
        }
        
        .timeline-content small {
            color: #6c757d;
        }
        
        .partnership-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .partnership-model-badge {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-handshake me-2"></i>South Safari
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="connections.php">My Connections</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../partnerships.php">Browse Opportunities</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i><?php echo escape($user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <div class="connection-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">Partnership Connection Details</h1>
                    <p class="mb-0">Connection ID: #<?php echo str_pad($connection['id'], 6, '0', STR_PAD_LEFT); ?></p>
                </div>
                <div class="col-md-4 text-md-end">
                    <span class="connection-status status-<?php echo $connection['status']; ?>">
                        <?php echo CONNECTION_STATUSES[$connection['status']] ?? ucfirst($connection['status']); ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-4">
        <?php displayFlashMessage(); ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo escape($error); ?></div>
        <?php endif; ?>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Partnership Summary -->
                <div class="partnership-summary">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h4 class="mb-2"><?php echo escape($connection['title']); ?></h4>
                            <span class="badge bg-secondary me-2"><?php echo escape($connection['category']); ?></span>
                            <span class="partnership-model-badge">
                                <?php echo escape($connection['revenue_split'] ?: PARTNERSHIP_MODELS[$connection['partnership_model']]); ?>
                            </span>
                        </div>
                        <a href="../partnership.php?slug=<?php echo $connection['slug']; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt me-1"></i>View Partnership
                        </a>
                    </div>
                    <p class="text-muted mb-0"><?php echo escape($connection['short_description']); ?></p>
                </div>

                <!-- Connection Details -->
                <div class="content-card">
                    <h3 class="section-title">Your Connection Request</h3>
                    
                    <div class="info-item">
                        <div class="info-label">Partnership Interest</div>
                        <div class="info-value"><?php echo nl2br(escape($connection['partnership_interest'])); ?></div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Relevant Experience & Expertise</div>
                        <div class="info-value"><?php echo nl2br(escape($connection['relevant_experience'])); ?></div>
                    </div>

                    <?php if ($connection['portfolio_items']): ?>
                        <div class="info-item">
                            <div class="info-label">Portfolio & Previous Work</div>
                            <div class="info-value"><?php echo nl2br(escape($connection['portfolio_items'])); ?></div>
                        </div>
                    <?php endif; ?>

                    <div class="info-item">
                        <div class="info-label">Proposed Partnership Approach</div>
                        <div class="info-value"><?php echo nl2br(escape($connection['proposed_approach'])); ?></div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Availability & Timeline</div>
                        <div class="info-value"><?php echo escape(str_replace('_', ' ', ucfirst($connection['availability']))); ?></div>
                    </div>

                    <?php if ($connection['additional_notes']): ?>
                        <div class="info-item">
                            <div class="info-label">Additional Notes</div>
                            <div class="info-value"><?php echo nl2br(escape($connection['additional_notes'])); ?></div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Review Notes -->
                <?php if ($connection['review_notes']): ?>
                    <div class="content-card">
                        <h3 class="section-title">Review Notes</h3>
                        <div class="info-value"><?php echo nl2br(escape($connection['review_notes'])); ?></div>
                        <?php if ($connection['admin_name']): ?>
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>Reviewed by <?php echo escape($connection['admin_name']); ?>
                                <?php if ($connection['reviewed_at']): ?>
                                    on <?php echo formatDateTime($connection['reviewed_at']); ?>
                                <?php endif; ?>
                            </small>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Connection Timeline -->
                <div class="content-card">
                    <h5 class="mb-3">Connection Timeline</h5>
                    
                    <div class="timeline-item">
                        <div class="timeline-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>Connection Submitted</h6>
                            <small><?php echo formatDateTime($connection['submitted_at']); ?></small>
                        </div>
                    </div>

                    <?php if ($connection['status'] === 'reviewing'): ?>
                        <div class="timeline-item">
                            <div class="timeline-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>Under Review</h6>
                                <small>Your connection is being reviewed</small>
                            </div>
                        </div>
                    <?php elseif ($connection['status'] === 'approved'): ?>
                        <div class="timeline-item">
                            <div class="timeline-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>Connection Approved</h6>
                                <small>
                                    <?php if ($connection['reviewed_at']): ?>
                                        <?php echo formatDateTime($connection['reviewed_at']); ?>
                                    <?php else: ?>
                                        Approved
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                    <?php elseif ($connection['status'] === 'rejected'): ?>
                        <div class="timeline-item">
                            <div class="timeline-icon">
                                <i class="fas fa-times"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>Connection Not Approved</h6>
                                <small>
                                    <?php if ($connection['reviewed_at']): ?>
                                        <?php echo formatDateTime($connection['reviewed_at']); ?>
                                    <?php else: ?>
                                        Not approved at this time
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                    <?php elseif ($connection['status'] === 'withdrawn'): ?>
                        <div class="timeline-item">
                            <div class="timeline-icon">
                                <i class="fas fa-undo"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>Connection Withdrawn</h6>
                                <small>You withdrew this connection request</small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Actions -->
                <div class="content-card">
                    <h6 class="mb-3">Actions</h6>
                    
                    <div class="d-grid gap-2">
                        <a href="connections.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Connections
                        </a>
                        
                        <a href="../partnership.php?slug=<?php echo $connection['slug']; ?>" class="btn btn-outline-info">
                            <i class="fas fa-external-link-alt me-2"></i>View Partnership Details
                        </a>

                        <?php if (in_array($connection['status'], ['submitted', 'reviewing'])): ?>
                            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#withdrawModal">
                                <i class="fas fa-times me-2"></i>Withdraw Connection
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Status Information -->
                <div class="content-card">
                    <h6 class="mb-3">Status Information</h6>
                    
                    <?php if ($connection['status'] === 'submitted'): ?>
                        <p class="text-muted small">Your connection request has been submitted and is awaiting initial review. We typically review connections within 2-3 business days.</p>
                    <?php elseif ($connection['status'] === 'reviewing'): ?>
                        <p class="text-muted small">Your connection request is currently under detailed review. We'll notify you of the decision soon.</p>
                    <?php elseif ($connection['status'] === 'approved'): ?>
                        <p class="text-success small">Congratulations! Your connection has been approved. We'll be in touch soon to discuss next steps for the partnership.</p>
                    <?php elseif ($connection['status'] === 'rejected'): ?>
                        <p class="text-muted small">Your connection request was not approved at this time. Feel free to explore other partnership opportunities that might be a better fit.</p>
                    <?php elseif ($connection['status'] === 'withdrawn'): ?>
                        <p class="text-muted small">You have withdrawn this connection request. You can still explore other partnership opportunities.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Withdraw Confirmation Modal -->
    <div class="modal fade" id="withdrawModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Withdraw Connection Request</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to withdraw your connection request for "<strong><?php echo escape($connection['title']); ?></strong>"?</p>
                    <p class="text-warning small">This action cannot be undone. You would need to submit a new connection request to reconnect with this partnership opportunity.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" style="display: inline;">
                        <?php echo getCSRFInput(); ?>
                        <input type="hidden" name="action" value="withdraw">
                        <button type="submit" class="btn btn-danger">Withdraw Connection</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
