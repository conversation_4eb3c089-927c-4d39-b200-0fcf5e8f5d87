<?php
/**
 * South Safari Partnership Platform - Database Connection & Utilities
 * Version 2.0 - Partnership-Centric & Feature-Focused
 * Created: 2025-01-18
 */

// Prevent direct access
if (!defined('SS_INIT')) {
    die('Direct access not permitted');
}

class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $database;
    private $username;
    private $password;
    private $charset;

    private function __construct() {
        $this->host = DB_HOST;
        $this->database = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
        
        $this->connect();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->database};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
            if (SS_DEBUG) {
                error_log("Database connected successfully");
            }
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            if (SS_DEBUG) {
                die("Database connection failed: " . $e->getMessage());
            } else {
                die("Database connection failed. Please try again later.");
            }
        }
    }

    public function getConnection() {
        return $this->connection;
    }

    /**
     * Execute a prepared statement with parameters
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query error: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Database query failed");
        }
    }

    /**
     * Fetch a single row
     */
    public function fetchRow($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Fetch all rows
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Insert a record and return the last insert ID
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->connection->lastInsertId();
    }

    /**
     * Update records
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Delete records
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Count records
     */
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->fetchRow($sql, $params);
        return (int) $result['count'];
    }

    /**
     * Check if a record exists
     */
    public function exists($table, $where, $params = []) {
        return $this->count($table, $where, $params) > 0;
    }

    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->connection->rollback();
    }

    /**
     * Get table structure for debugging
     */
    public function getTableStructure($table) {
        return $this->fetchAll("DESCRIBE {$table}");
    }

    /**
     * Escape string for LIKE queries
     */
    public function escapeLike($string) {
        return str_replace(['%', '_'], ['\%', '\_'], $string);
    }

    /**
     * Build pagination query
     */
    public function paginate($sql, $params, $page, $perPage) {
        $offset = ($page - 1) * $perPage;
        $sql .= " LIMIT {$perPage} OFFSET {$offset}";
        return $this->fetchAll($sql, $params);
    }

    /**
     * Get total pages for pagination
     */
    public function getTotalPages($countSql, $params, $perPage) {
        $result = $this->fetchRow($countSql, $params);
        $totalRecords = $result['count'];
        return ceil($totalRecords / $perPage);
    }

    /**
     * Log database activity
     */
    public function logActivity($userId, $action, $entityType, $entityId, $oldValues = null, $newValues = null) {
        $data = [
            'user_id' => $userId,
            'action' => $action,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'old_values' => $oldValues ? json_encode($oldValues) : null,
            'new_values' => $newValues ? json_encode($newValues) : null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        return $this->insert('activity_log', $data);
    }

    /**
     * Get system setting
     */
    public function getSetting($key, $default = null) {
        $result = $this->fetchRow(
            "SELECT setting_value, setting_type FROM system_settings WHERE setting_key = ?",
            [$key]
        );
        
        if (!$result) {
            return $default;
        }
        
        $value = $result['setting_value'];
        
        // Convert based on type
        switch ($result['setting_type']) {
            case 'boolean':
                return (bool) $value;
            case 'integer':
                return (int) $value;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * Set system setting
     */
    public function setSetting($key, $value, $type = 'string', $description = null) {
        // Convert value based on type
        switch ($type) {
            case 'boolean':
                $value = $value ? '1' : '0';
                break;
            case 'json':
                $value = json_encode($value);
                break;
            default:
                $value = (string) $value;
        }
        
        $data = [
            'setting_key' => $key,
            'setting_value' => $value,
            'setting_type' => $type,
            'description' => $description,
            'updated_by' => $_SESSION['user_id'] ?? null
        ];
        
        // Try to update first, then insert if not exists
        $updated = $this->update(
            'system_settings',
            ['setting_value' => $value, 'updated_by' => $data['updated_by']],
            'setting_key = :key',
            ['key' => $key]
        );
        
        if ($updated === 0) {
            return $this->insert('system_settings', $data);
        }
        
        return true;
    }

    /**
     * Close connection (called automatically on script end)
     */
    public function __destruct() {
        $this->connection = null;
    }
}

// Global database helper functions
function db() {
    return Database::getInstance();
}

function dbQuery($sql, $params = []) {
    return db()->query($sql, $params);
}

function dbFetchRow($sql, $params = []) {
    return db()->fetchRow($sql, $params);
}

function dbFetchAll($sql, $params = []) {
    return db()->fetchAll($sql, $params);
}

function dbInsert($table, $data) {
    return db()->insert($table, $data);
}

function dbUpdate($table, $data, $where, $whereParams = []) {
    return db()->update($table, $data, $where, $whereParams);
}

function dbDelete($table, $where, $params = []) {
    return db()->delete($table, $where, $params);
}

function dbCount($table, $where = '1=1', $params = []) {
    return db()->count($table, $where, $params);
}

function dbExists($table, $where, $params = []) {
    return db()->exists($table, $where, $params);
}
